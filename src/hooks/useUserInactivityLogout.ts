'use client';

import { useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { performCompleteLogout, getCognitoTokensFromCookies } from '@/utils/auth';

interface UserInactivityOptions {
  timeout?: number; // Timeout in minutes (default: 30)
  enabled?: boolean; // Enable/disable inactivity tracking (default: true)
  checkInterval?: number; // Check interval in milliseconds (default: 60 seconds)
  events?: string[]; // Events to track for user activity
  onWarning?: (timeLeft: number) => void; // Callback when warning should be shown
  warningTime?: number; // Show warning X minutes before logout (default: 5)
}

/**
 * Hook that tracks user inactivity and automatically logs out users after a specified timeout
 * Tracks mouse movements, clicks, keyboard events, and touch events
 */
export const useUserInactivityLogout = (options: UserInactivityOptions = {}) => {
  const {
    timeout = 30, // 30 minutes default
    enabled = true,
    checkInterval = 10000, // 10 seconds
    events = ['mousedown', 'keydown', 'scroll', 'touchstart', 'click'],
    onWarning,
    warningTime = 5 // 5 minutes warning
  } = options;

  const router = useRouter();
  const lastActivityRef = useRef<number>(Date.now());
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningShownRef = useRef<boolean>(false);
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastLogTimeRef = useRef<number>(0);
  const lastActivityUpdateRef = useRef<number>(0);

  // Update last activity timestamp with throttled logging and updates
  const updateActivity = useCallback(() => {
    const now = Date.now();

    // Throttle activity updates to once per second to prevent excessive resets
    if (now - lastActivityUpdateRef.current < 1000) {
      return;
    }

    lastActivityRef.current = now;
    lastActivityUpdateRef.current = now;
    warningShownRef.current = false;

    // Only log once every 5 seconds to prevent spam
    if (now - lastLogTimeRef.current > 5000) {
      console.log('🔄 User activity detected, resetting inactivity timer');
      lastLogTimeRef.current = now;
    }
  }, []);

  // Perform logout due to inactivity
  const performInactivityLogout = useCallback(async () => {
    console.log('⏰ User inactive for', timeout, 'minutes. Logging out...');
    
    try {
      // Clear all timers
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
        checkIntervalRef.current = null;
      }

      // Perform complete logout
      performCompleteLogout();
      
      // Redirect to login page
      router.push('/login?reason=inactivity');
    } catch (error) {
      console.error('Error during inactivity logout:', error);
      // Force redirect even if logout fails
      window.location.href = '/login?reason=inactivity';
    }
  }, [timeout, router]);

  // Check if user should be logged out
  const checkInactivity = useCallback(() => {
    // Skip if disabled
    if (!enabled) {
      console.log('🔍 Inactivity tracking disabled, skipping check');
      return;
    }

    const tokens = getCognitoTokensFromCookies();
    console.log('🔍 Token check:', {
      hasAccessToken: !!tokens.accessToken,
      hasIdToken: !!tokens.idToken,
      accessTokenLength: tokens.accessToken?.length || 0
    });

    if (!tokens.accessToken) {
      console.log('🔍 No access token found, skipping inactivity check');
      return;
    }

    const now = Date.now();
    const timeSinceLastActivity = now - lastActivityRef.current;
    const timeoutMs = timeout * 60 * 1000; // Convert minutes to milliseconds
    const warningTimeMs = warningTime * 60 * 1000; // Convert minutes to milliseconds
    const timeLeft = timeoutMs - timeSinceLastActivity;

    console.log('🔍 Inactivity check:', {
      timeSinceLastActivitySeconds: Math.round(timeSinceLastActivity / 1000),
      timeoutMinutes: timeout,
      timeLeftSeconds: Math.round(timeLeft / 1000),
      willLogout: timeSinceLastActivity >= timeoutMs
    });

    // Show warning if approaching timeout
    if (timeLeft <= warningTimeMs && timeLeft > 0 && !warningShownRef.current && onWarning) {
      warningShownRef.current = true;
      const minutesLeft = Math.ceil(timeLeft / 1000 / 60);
      console.log('⚠️ Showing inactivity warning:', minutesLeft, 'minutes left');
      onWarning(minutesLeft);
    }

    // Logout if timeout exceeded
    if (timeSinceLastActivity >= timeoutMs) {
      performInactivityLogout();
    }
  }, [enabled, timeout, warningTime, onWarning, performInactivityLogout]);

  // Set up activity listeners
  useEffect(() => {
    if (!enabled) return;

    console.log('🔧 Setting up user inactivity tracking:', {
      timeout: `${timeout} minutes`,
      checkInterval: `${checkInterval}ms`,
      events: events.join(', '),
      warningTime: `${warningTime} minutes`
    });

    // Add event listeners for user activity
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // Set up periodic inactivity checking
    checkIntervalRef.current = setInterval(checkInactivity, checkInterval);

    // Initial activity timestamp
    updateActivity();

    return () => {
      console.log('🧹 Cleaning up inactivity tracking');
      
      // Remove event listeners
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });

      // Clear intervals
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
        checkIntervalRef.current = null;
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [enabled, timeout, checkInterval, events, updateActivity, checkInactivity]);

  // Reset activity when user returns to tab
  useEffect(() => {
    if (!enabled) return;

    const handleFocus = () => {
      console.log('👁️ Window focus detected, resetting inactivity timer');
      updateActivity();
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('👁️ Tab became visible, resetting inactivity timer');
        updateActivity();
      }
    };

    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, updateActivity]);

  // Return utility functions for manual control
  return {
    resetActivity: updateActivity,
    checkInactivity,
    getLastActivity: () => lastActivityRef.current,
    getTimeUntilLogout: () => {
      const timeSinceLastActivity = Date.now() - lastActivityRef.current;
      const timeoutMs = timeout * 60 * 1000;
      return Math.max(0, timeoutMs - timeSinceLastActivity);
    },
    // Debug function to get current status
    getDebugStatus: () => ({
      enabled,
      timeout,
      warningTime,
      checkInterval,
      lastActivity: lastActivityRef.current,
      timeSinceLastActivity: Date.now() - lastActivityRef.current,
      timeUntilLogout: Math.max(0, (timeout * 60 * 1000) - (Date.now() - lastActivityRef.current)),
      warningShown: warningShownRef.current
    })
  };
};
