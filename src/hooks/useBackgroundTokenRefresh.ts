'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import jwt from 'jsonwebtoken';
import { refreshTokenIfNeeded, isAccessTokenExpired } from '@/utils/simpleTokenRefresh';
import { getCognitoTokensFromCookies, performCompleteLogout } from '@/utils/auth';

interface BackgroundRefreshOptions {
  checkInterval?: number; // Check every X milliseconds (default: 30 seconds)
  refreshBuffer?: number; // Refresh X minutes before expiry (default: 5 minutes)
}

export const useBackgroundTokenRefresh = (options: BackgroundRefreshOptions = {}) => {
  const {
    checkInterval = 30000, // 30 seconds
    refreshBuffer = 5 // 5 minutes
  } = options;

  const router = useRouter();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);

  // Check if token needs refresh (within buffer time)
  const shouldRefreshSoon = (accessToken: string): boolean => {
    try {
      const decoded = jwt.decode(accessToken, { complete: true });
      if (!decoded || typeof decoded === 'string' || !decoded.payload) {
        return true;
      }

      const payload = decoded.payload as any;
      const currentTime = Math.floor(Date.now() / 1000);
      const expiresAt = payload.exp;
      const timeUntilExpiry = expiresAt - currentTime;
      const bufferSeconds = refreshBuffer * 60;

      return timeUntilExpiry <= bufferSeconds;
    } catch (error) {
      return true;
    }
  };

  // Silent token refresh
  const performSilentRefresh = async (): Promise<boolean> => {
    if (isRefreshingRef.current) {
      return false;
    }

    try {
      isRefreshingRef.current = true;
      console.log('🔄 Performing silent token refresh...');

      const success = await refreshTokenIfNeeded();
      
      if (success) {
        console.log('✅ Silent token refresh successful');
        return true;
      } else {
        console.error('❌ Silent token refresh failed - user needs to login');
        
        // Clear tokens and redirect to login
        performCompleteLogout();
        router.push('/login');
        return false;
      }
    } catch (error: any) {
      console.error('❌ Silent refresh error:', error);
      
      // If refresh fails, logout user
      performCompleteLogout();
      router.push('/login');
      return false;
    } finally {
      isRefreshingRef.current = false;
    }
  };

  // Check tokens and refresh if needed
  const checkAndRefreshTokens = async (): Promise<void> => {
    try {
      // console.log('🔍 Background token check running...');
      const tokens = getCognitoTokensFromCookies();

      if (!tokens.accessToken) {
        // console.log('🔍 No access token found, skipping background check');
        return; // No token, user not logged in
      }

      const isExpired = isAccessTokenExpired(tokens.accessToken);
      const willExpireSoon = shouldRefreshSoon(tokens.accessToken);

      // console.log('🔍 Token status:', {
      //   isExpired,
      //   willExpireSoon,
      //   refreshBuffer: `${refreshBuffer} minutes`
      // });

      // If token is already expired, refresh immediately
      if (isExpired) {
        console.log('🔄 Token expired, triggering background refresh...');
        await performSilentRefresh();
        return;
      }

      // If token will expire soon, refresh proactively
      if (willExpireSoon) {
        console.log('🔄 Token will expire soon, triggering proactive refresh...');
        await performSilentRefresh();
      } else {
        console.log('✅ Token is valid, no refresh needed');
      }
    } catch (error) {
      console.error('❌ Error checking tokens in background:', error);
    }
  };

  // Set up background token checking
  useEffect(() => {
    // console.log('🔧 Setting up background token refresh:', {
    //   checkInterval: `${checkInterval}ms`,
    //   refreshBuffer: `${refreshBuffer} minutes`
    // });

    // Set up interval to check tokens periodically
    intervalRef.current = setInterval(() => {
      // console.log('⏰ Background token check interval triggered');
      checkAndRefreshTokens();
    }, checkInterval);

    // Initial check
    // console.log('🚀 Running initial background token check');
    checkAndRefreshTokens();

    return () => {
      // console.log('🧹 Cleaning up background token refresh interval');
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [checkInterval, refreshBuffer, checkAndRefreshTokens]);

  // Check tokens when user returns to tab
  useEffect(() => {
    const handleFocus = () => {
      // console.log('👁️ Window focus detected, checking tokens...');
      checkAndRefreshTokens();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [checkAndRefreshTokens]);

  // Listen for storage changes (tokens updated in another tab)
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key && event.key.includes('cognito')) {
        // console.log('🔄 Token change detected in another tab:', event.key);
        setTimeout(() => {
          // console.log('🔄 Checking tokens after storage change...');
          checkAndRefreshTokens();
        }, 1000);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [checkAndRefreshTokens]);

  return {
    performSilentRefresh,
    checkAndRefreshTokens
  };
};
