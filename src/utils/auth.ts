import jwt from "jsonwebtoken";

// Cognito token sync utilities
interface CognitoTokens {
  accessToken: string | null;
  idToken: string | null;
  refreshToken: string | null;
}

// Get Cognito configuration for localStorage key generation
const getCognitoConfig = () => {
  const userPoolClientId = process.env.NEXT_PUBLIC_AWS_CLIENT_ID;
  const region = process.env.NEXT_PUBLIC_AWS_REGION;
  return { userPoolClientId, region };
};

// Generate Cognito localStorage keys
const getCognitoStorageKeys = (username?: string) => {
  const { userPoolClientId } = getCognitoConfig();
  const baseKey = `CognitoIdentityServiceProvider.${userPoolClientId}`;

  if (username) {
    return {
      accessToken: `${baseKey}.${username}.accessToken`,
      idToken: `${baseKey}.${username}.idToken`,
      refreshToken: `${baseKey}.${username}.refreshToken`,
      userData: `${baseKey}.${username}.userData`,
      lastAuthUser: `${baseKey}.LastAuthUser`,
    };
  }

  return {
    lastAuthUser: `${baseKey}.LastAuthUser`,
    accessToken: undefined,
    idToken: undefined,
    refreshToken: undefined,
    userData: undefined,
  };
};

// Get current authenticated user from Cognito localStorage
export const getCognitoCurrentUser = (): string | null => {
  if (typeof window === "undefined") return null;

  try {
    const { lastAuthUser } = getCognitoStorageKeys();
    return localStorage.getItem(lastAuthUser);
  } catch (error) {
    console.error("Error getting current Cognito user:", error);
    return null;
  }
};

// Get Cognito tokens from localStorage
export const getCognitoTokens = (username?: string): CognitoTokens => {
  if (typeof window === "undefined") {
    return { accessToken: null, idToken: null, refreshToken: null };
  }

  try {
    const currentUser = username || getCognitoCurrentUser();
    if (!currentUser) {
      return { accessToken: null, idToken: null, refreshToken: null };
    }

    const keys = getCognitoStorageKeys(currentUser);

    // Check if keys exist before accessing localStorage
    if (!keys.accessToken || !keys.idToken || !keys.refreshToken) {
      return { accessToken: null, idToken: null, refreshToken: null };
    }

    return {
      accessToken: localStorage.getItem(keys.accessToken),
      idToken: localStorage.getItem(keys.idToken),
      refreshToken: localStorage.getItem(keys.refreshToken),
    };
  } catch (error) {
    console.error("Error getting Cognito tokens:", error);
    return { accessToken: null, idToken: null, refreshToken: null };
  }
};

// Sync Cognito tokens to cookies with proper expiry based on token expiration
export const syncCognitoTokensToCookies = async (
  username?: string,
  days: number = 7
): Promise<boolean> => {
  const pollInterval = 100;
  const maxWait = 3000;
  let waited = 0;
  while (waited < maxWait) {
    const tokens = getCognitoTokens(username);
    if (tokens.accessToken && tokens.idToken) {
      // Set cookies as before
      let tokenExpiry = 60 * 60;
      try {
        const decoded = jwt.decode(tokens.accessToken, { complete: true });
        if (decoded && typeof decoded !== "string" && decoded.payload) {
          const payload = decoded.payload as any;
          const currentTime = Math.floor(Date.now() / 1000);
          const expiresIn = payload.exp - currentTime;
          if (expiresIn > 300) {
            tokenExpiry = expiresIn;
          }
        }
      } catch (error) {
        console.warn("Could not decode token expiry, using default 1 hour");
      }
      const cookieOptions = `path=/; max-age=${tokenExpiry}; SameSite=Lax; Secure`;
      document.cookie = `cognito_access_token=${tokens.accessToken}; ${cookieOptions}`;
      document.cookie = `cognito_id_token=${tokens.idToken}; ${cookieOptions}`;
      if (tokens.refreshToken) {
        const refreshMaxAge = 60 * 60 * 24 * 30;
        const refreshCookieOptions = `path=/; max-age=${refreshMaxAge}; SameSite=Lax; Secure`;
        document.cookie = `cognito_refresh_token=${tokens.refreshToken}; ${refreshCookieOptions}`;
      }
      document.cookie = `token=${tokens.accessToken}; ${cookieOptions}`;
      return true;
    }
    await new Promise((res) => setTimeout(res, pollInterval));
    waited += pollInterval;
  }
  console.warn("⚠️ Tokens not found in localStorage after waiting");
  return false;
};

// Get tokens from cookies
export const getCognitoTokensFromCookies = (): CognitoTokens => {
  if (typeof document === "undefined") {
    return { accessToken: null, idToken: null, refreshToken: null };
  }

  const cookies = document.cookie.split(";");
  const getCookie = (name: string) => {
    const cookie = cookies.find((c) => c.trim().startsWith(`${name}=`));
    return cookie ? cookie.split("=")[1] : null;
  };

  return {
    accessToken: getCookie("cognito_access_token"),
    idToken: getCookie("cognito_id_token"),
    refreshToken: getCookie("cognito_refresh_token"),
  };
};

// Legacy functions for backward compatibility
export const setAuthToken = (token: string, days: number = 7) => {
  document.cookie = `token=${token}; path=/; max-age=${
    60 * 60 * 24 * days
  }; SameSite=Lax`;
  console.log("Token set in cookie:", token);
};

export const getAuthToken = (): string | null => {
  if (typeof document === "undefined") return null;

  const cookies = document.cookie.split(";");
  const tokenCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("token=")
  );

  if (tokenCookie) {
    const token = tokenCookie.split("=")[1];
    console.log("Token retrieved from cookie:", token);
    return token;
  }

  console.log("No token found in cookies");
  return null;
};

export const removeAuthToken = () => {
  // Remove all auth-related cookies
  const cookiesToRemove = [
    "token",
    "cognito_access_token",
    "cognito_id_token",
    "cognito_refresh_token",
  ];

  cookiesToRemove.forEach((cookieName) => {
    document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  });

  localStorage.clear();

  console.log("All auth tokens removed from cookies");
};

export const logout = () => {
  removeAuthToken();
  window.location.href = "/";
};

// Complete logout - clear all tokens and data
export const performCompleteLogout = (): void => {
  try {
    // Clear all auth-related cookies
    const cookiesToClear = [
      "token",
      "cognito_access_token",
      "cognito_id_token",
      "cognito_refresh_token",
    ];

    const clearOptions =
      "path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax";

    cookiesToClear.forEach((cookieName) => {
      document.cookie = `${cookieName}=; ${clearOptions}`;
    });

    // Clear Cognito data from localStorage
    const currentUser = getCognitoCurrentUser();
    const userPoolClientId = process.env.NEXT_PUBLIC_AWS_CLIENT_ID;

    if (userPoolClientId) {
      // Clear user-specific tokens
      if (currentUser) {
        const keyPrefix = `CognitoIdentityServiceProvider.${userPoolClientId}.${currentUser}`;

        const keysToRemove = [
          `${keyPrefix}.accessToken`,
          `${keyPrefix}.idToken`,
          `${keyPrefix}.refreshToken`,
          `${keyPrefix}.clockDrift`,
          `${keyPrefix}.userData`,
          `${keyPrefix}.deviceKey`,
          `${keyPrefix}.randomPasswordKey`,
          `${keyPrefix}.deviceGroupKey`,
        ];

        keysToRemove.forEach((key) => {
          localStorage.removeItem(key);
        });
      }

      // Clear general Cognito keys
      localStorage.removeItem(
        `CognitoIdentityServiceProvider.${userPoolClientId}.LastAuthUser`
      );

      // Clear any remaining Cognito keys
      Object.keys(localStorage).forEach((key) => {
        if (key.includes("CognitoIdentityServiceProvider")) {
          localStorage.removeItem(key);
        }
      });
    }

    console.log("🧹 Complete logout performed - all tokens and data cleared");
  } catch (error) {
    console.error("❌ Error during complete logout:", error);
  }
};

export const isAuthenticated = (): boolean => {
  try {
    // Check both legacy token and Cognito tokens
    const legacyToken = getAuthToken();
    const cognitoTokens = getCognitoTokensFromCookies();

    const hasLegacyToken = !!(
      legacyToken &&
      legacyToken !== "undefined" &&
      legacyToken !== "null" &&
      legacyToken.trim() !== ""
    );
    const hasCognitoTokens = !!(
      cognitoTokens.accessToken && cognitoTokens.idToken
    );

    const isValid = hasLegacyToken || hasCognitoTokens;

    console.log("Client auth check:", {
      legacyToken: hasLegacyToken ? "exists" : "missing",
      cognitoTokens: hasCognitoTokens ? "exists" : "missing",
      isValid,
    });

    return isValid;
  } catch (error) {
    console.error("Error checking authentication:", error);
    return false;
  }
};
