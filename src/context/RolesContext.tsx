import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchRolesRequest,
  upsertRoleRequest,
  deleteRoleRequest,
  bulkDeleteRolesRequest,
} from "@/store/roles/redux";
import {
  selectRolesList,
  selectRolesLoading,
  selectRolesError,
  selectRolesStats,
  selectShouldRefreshRoles,
} from "@/store/roles/selector";
import { Role as RoleType } from "@/types/role";

interface RolesContextType {
  roles: RoleType[];
  loading: boolean;
  error: string | null;
  fetchRoles: () => void;
  getRoleBySlug: (slug: string) => RoleType | undefined;
  updateRole: (role: RoleType, data: any) => Promise<void>;
  createRole: (data: any) => Promise<void>;
  deleteRole: (slug: string) => Promise<void>;
}

const RolesContext = createContext<RolesContextType | undefined>(undefined);

export const RolesProvider = ({ children }: { children: ReactNode }) => {
  const dispatch = useAppDispatch();
  const roles = useAppSelector(selectRolesList);
  const loading = useAppSelector(selectRolesLoading);
  const error = useAppSelector(selectRolesError);
  const shouldRefresh = useAppSelector(selectShouldRefreshRoles);

  // Memoize fetchRoles
  const fetchRoles = useCallback(() => {
    dispatch(fetchRolesRequest());
  }, [dispatch]);

  useEffect(() => {
    if (shouldRefresh) {
      dispatch(fetchRolesRequest());
    }
  }, [dispatch, shouldRefresh]);

  const getRoleBySlug = (slug: string) => roles.find((r) => r.slug === slug);

  const updateRole = async (role: RoleType, data: any) => {
    await dispatch(
      upsertRoleRequest({ ...data, role: { ...data.role, name: role.name } })
    );
  };

  const createRole = async (data: any) => {
    await dispatch(upsertRoleRequest(data));
  };

  const deleteRole = async (slug: string) => {
    await dispatch(deleteRoleRequest(slug));
  };

  return (
    <RolesContext.Provider
      value={{
        roles,
        loading,
        error,
        fetchRoles,
        getRoleBySlug,
        updateRole,
        createRole,
        deleteRole,
      }}
    >
      {children}
    </RolesContext.Provider>
  );
};

export const useRoles = () => {
  const context = useContext(RolesContext);
  if (!context) {
    throw new Error("useRoles must be used within a RolesProvider");
  }
  return context;
};
