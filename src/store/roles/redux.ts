import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Role, AllRolesPermissionsResponse, RoleWithPermissions, UpdateRoleData } from "@/types/role";

interface RolesState {
  roles: Role[];
  allRolesPermissions: AllRolesPermissionsResponse | null; // Updated type
  loading: boolean;
  error: string | null;
  selectedRole: Role | null;
}

const initialState: RolesState = {
  roles: [],
  allRolesPermissions: null,
  loading: false,
  error: null,
  selectedRole: null,
};

const rolesSlice = createSlice({
  name: "roles",
  initialState,
  reducers: {
    fetchRolesRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchRolesSuccess(state, action: PayloadAction<Role[]>) {
      state.loading = false;
      state.roles = action.payload;
      state.error = null;
    },
    fetchRolesFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    upsertRoleRequest(state) {
      state.loading = true;
      state.error = null;
    },
    upsertRoleSuccess(state) {
      state.loading = false;
      state.error = null;
  
    },
    upsertRoleFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    deleteRoleRequest(state, _action: PayloadAction<string>) {
      state.loading = true;
      state.error = null;
    },
    deleteRoleSuccess(state) {
      state.loading = false;
      state.error = null;
    },
    deleteRoleFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    bulkDeleteRolesRequest(state, _action: PayloadAction<string[]>) {
      state.loading = true;
      state.error = null;
    },
    bulkDeleteRolesSuccess(state) {
      state.loading = false;
      state.error = null;
    },
    bulkDeleteRolesFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    setSelectedRole(state, action: PayloadAction<Role | null>) {
      state.selectedRole = action.payload;
    },
    clearRolesError(state) {
      state.error = null;
    },
    fetchAllRolesPermissionsRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchAllRolesPermissionsSuccess(state, action: PayloadAction<RoleWithPermissions[]>) {
      state.loading = false;
      state.allRolesPermissions = {
        roles: action.payload,
        total_roles: action.payload.length,
      };
      state.error = null;
    },
    fetchAllRolesPermissionsFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchRolesRequest,
  fetchRolesSuccess,
  fetchRolesFailure,
  upsertRoleRequest,
  upsertRoleSuccess,
  upsertRoleFailure,
  deleteRoleRequest,
  deleteRoleSuccess,
  deleteRoleFailure,
  bulkDeleteRolesRequest,
  bulkDeleteRolesSuccess,
  bulkDeleteRolesFailure,
  setSelectedRole,
  clearRolesError,
  fetchAllRolesPermissionsRequest,
  fetchAllRolesPermissionsSuccess,
  fetchAllRolesPermissionsFailure,
} = rolesSlice.actions;

export default rolesSlice.reducer;
