import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, put, takeLatest, select } from "redux-saga/effects";
import { membersApiService } from "@/services/membersApiService";
import {
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,
  selectMembersArray,
  selectMembersLastFetched,
} from "./redux";

function* fetchMembersSaga(): Generator<any, void, any> {
  try {
    const members = yield select(selectMembersArray);
    const lastFetched = yield select(selectMembersLastFetched);
    const now = Date.now();
    const twentyMinutes = 20 * 60 * 1000;
    const lastFetchedTime = lastFetched ? new Date(lastFetched).getTime() : 0;
    if (members.length === 0 || now - lastFetchedTime > twentyMinutes) {
      const apiMembers = yield call(membersApiService.getMembers);
      yield put(fetchMembersSuccess(apiMembers));
    } // else: do nothing, cache is fresh
  } catch (error: any) {
    yield put(fetchMembersFailure(error.message || "Failed to fetch members"));
  }
}

export function* membersSaga(): Generator<any, void, any> {
  yield all([takeLatest(fetchMembersRequest.type, fetchMembersSaga)]);
}
