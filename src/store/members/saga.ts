import { PayloadAction } from "@reduxjs/toolkit";
import { all, call, put, takeLatest } from "redux-saga/effects";
import { membersApiService } from "@/services/membersApiService";
import {
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,
} from "./redux";

function* fetchMembersSaga(): Generator<any, void, any> {
  try {
    const members = yield call(membersApiService.getMembers);
    yield put(fetchMembersSuccess(members));
  } catch (error: any) {
    yield put(fetchMembersFailure(error.message || "Failed to fetch members"));
  }
}

export function* membersSaga(): Generator<any, void, any> {
  yield all([takeLatest(fetchMembersRequest.type, fetchMembersSaga)]);
}
