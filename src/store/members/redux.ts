import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Member } from "@/types/member";

interface MembersState {
  members: Member[];
  loading: boolean;
  error: string | null;
}

const initialState: MembersState = {
  members: [],
  loading: false,
  error: null,
};

const membersSlice = createSlice({
  name: "members",
  initialState,
  reducers: {
    fetchMembersRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchMembersSuccess(state, action: PayloadAction<Member[]>) {
      state.loading = false;
      state.members = action.payload;
      state.error = null;
    },
    fetchMembersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    clearMembersError(state) {
      state.error = null;
    },
  },
});

export const {
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,
  clearMembersError,
} = membersSlice.actions;

export default membersSlice.reducer;
