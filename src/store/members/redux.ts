import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Member } from "@/types/member";

interface MembersState {
  members: Member[];
  membersById: Record<number, Member>;
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
}

const initialState: MembersState = {
  members: [],
  membersById: {},
  loading: false,
  error: null,
  lastFetched: null,
};

const membersSlice = createSlice({
  name: "members",
  initialState,
  reducers: {
    fetchMembersRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchMembersSuccess(state, action: PayloadAction<Member[]>) {
      state.loading = false;
      state.members = action.payload;
      state.membersById = {};
      action.payload.forEach((m) => {
        state.membersById[m.id] = m;
      });
      state.lastFetched = new Date().toISOString();
      state.error = null;
    },
    fetchMembersFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    clearMembersError(state) {
      state.error = null;
    },
  },
});

export const {
  fetchMembersRequest,
  fetchMembersSuccess,
  fetchMembersFailure,
  clearMembersError,
} = membersSlice.actions;

export default membersSlice.reducer;

// Selectors
export const selectMembersArray = (state: any) => state.members.members;
export const selectMembersMap = (state: any) => state.members.membersById;
export const selectMembersLoading = (state: any) => state.members.loading;
export const selectMembersError = (state: any) => state.members.error;
export const selectMembersLastFetched = (state: any) =>
  state.members.lastFetched;
