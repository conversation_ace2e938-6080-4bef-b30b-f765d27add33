

function convertToGenerator<T extends any[], R>(
    fn: (...args: T) => R,
    failFn?: (error: Error, ...args: T) => void
): (...args: T) => Generator<R, void, unknown> {
    return function* (...args: T) {
        try {
            yield fn(...args);
        } catch (error) {
            if (failFn && error instanceof Error) {
                failFn(error, ...args);
            } else {
                throw error;
            }
        }
    };
}