import { all, fork } from "redux-saga/effects";
import { adminUserSaga } from "./adminUser/saga";
import { authSaga } from "./auth/saga";
import { rolesSaga } from "./roles/saga";
import { membersSaga } from "./members/saga";

const wrapper = (fn: any) => fork(fn);

const allFunction = [authSaga, rolesSaga, adminUserSaga, membersSaga];

const hocFn = allFunction.map((fn) => wrapper(fn));

export function* rootSaga() {
  yield all(hocFn);
}
