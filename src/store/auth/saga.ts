import { authApiService } from "@/services/authApiService";
import { LoginFormData, MFAFormData, User } from "@/types/auth";
import { syncCognitoTokensToCookies } from "@/utils/auth";
import { navigate } from "@/utils/routerService";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  all,
  call,
  fork,
  put,
  select,
  takeLatest
} from "redux-saga/effects";
import {
  confirmMFAFailure,
  confirmMFARequest,
  confirmMFASuccess,
  loginFailure,
  loginMFARequired,
  loginRequest,
  loginSuccess,
  logoutFailure,
  logoutRequest,
  logoutSuccess,
  refreshUserFailure,
  refreshUserRequest,
  refreshUserSuccess,
  setupTOTPFailure,
  setupTOTPRequest,
  setupTOTPSuccess,
} from "./redux";

function* loginSaga(
  action: PayloadAction<LoginFormData>
): Generator<any, void, any> {
  try {
    const result = yield call(authApiService.login, action.payload);
    if (result && result.isSignedIn === false && result.nextStep) {
      let mfaType = result.nextStep.signInStep;
      if (mfaType === "CONFIRM_SIGN_IN_WITH_TOTP_CODE") mfaType = "TOTP";
      if (mfaType === "CONFIRM_SIGN_IN_WITH_SMS_CODE") mfaType = "SMS";
      yield put(loginMFARequired({ mfaType }));
    } else {
      yield put(loginSuccess(result));
    }
  } catch (error: any) {
    yield put(loginFailure(error.message || "Login failed"));
  }
}

function* confirmMFASaga(
  action: PayloadAction<MFAFormData>
): Generator<any, void, any> {
  try {
    const state = yield select();
    const username =
      state.auth.user?.username || state.auth.loginData?.username || "";
    const result = yield call(
      authApiService.confirmMFA,
      username,
      action.payload.code
    );
    yield put(confirmMFASuccess(result));
    if (result && result.isSignedIn) {
      const user = yield call(authApiService.getCurrentUser);
      yield call(syncCognitoTokensToCookies, user.username);
      yield call(navigate, "/dashboard");
    }
  } catch (error: any) {
    yield put(confirmMFAFailure(error.message || "MFA confirmation failed"));
  }
}

function* setupTOTPSaga(
  action: PayloadAction<{ code: string }>
): Generator<any, void, any> {
  try {
    yield call(authApiService.setupTOTP, action.payload.code);
    yield put(setupTOTPSuccess());
  } catch (error: any) {
    yield put(setupTOTPFailure(error.message || "TOTP setup failed"));
  }
}

function* logoutSaga(): Generator<any, void, any> {
  try {
    yield call(authApiService.logout);
    yield put(logoutSuccess());
  } catch (error: any) {
    yield put(logoutFailure(error.message || "Logout failed"));
  }
}

function* refreshUserSaga(): Generator<any, void, any> {
  try {
    const user: User = yield call(authApiService.getCurrentUser);
    yield put(refreshUserSuccess(user));
  } catch (error: any) {
    yield put(refreshUserFailure(error.message || "Failed to refresh user"));
  }
}

export function* authSaga(): Generator<any, void, any> {
  yield all([
    takeLatest(loginRequest.type, loginSaga),
    takeLatest(confirmMFARequest.type, confirmMFASaga),
    takeLatest(setupTOTPRequest.type, setupTOTPSaga),
    takeLatest(logoutRequest.type, logoutSaga),
    takeLatest(refreshUserRequest.type, refreshUserSaga),
  ]);
}

export function* rootSaga() {
  yield all([
    fork(authSaga),
    // Add other sagas here as you create them
  ]);
}
