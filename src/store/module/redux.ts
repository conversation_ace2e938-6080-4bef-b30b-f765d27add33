import { createSlice, PayloadAction } from "@reduxjs/toolkit"

export interface ModuleType {
  name: string
  slug: string
  createdby: string
  updatedby: string
  datecreated: string
  dateupdated: string
}


export interface IModules  {
  loading: boolean
  error: string | null
  modules: ModuleType[]
}

const initialState: IModules = {
  loading: false,
  error: null,
  modules: []
}

const moduleSlice = createSlice({
  name: "modules",
  initialState,
  reducers: {
    fetchModulesRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchModulesSuccess(state, action: PayloadAction<ModuleType[]>) {
      state.loading = false;
      state.modules = action.payload;
      state.error = null;
    },
    fetchModulesFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    clearModulesError(state) {
      state.error = null;
    }
  }
});

export const {
  fetchModulesRequest,
  fetchModulesSuccess,
  fetchModulesFailure,
  clearModulesError
} = moduleSlice.actions;

export default moduleSlice.reducer;

