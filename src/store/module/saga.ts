import { all, call, fork, put, takeLatest } from "redux-saga/effects";
import { fetchModulesFailure, fetchModulesRequest, fetchModulesSuccess, ModuleType } from "./redux";
import { Mo<PERSON>le<PERSON><PERSON> } from "@/services/moduleAPI";


function* fetchModule(): Generator<any, void, ModuleType[]> {
    try {
        const response = yield call(ModuleApi.fetch);
        yield put(fetchModulesSuccess(response))
    } catch (error:any) {
        yield put(fetchModulesFailure(error.message || 'Failed to fetch module'))
    }
}

function* initModule() {
    yield put(fetchModulesRequest());
}



export function* moduleSaga() {
    yield all([initModule(),
    takeLatest(fetchModulesRequest.type, fetchModule)
    ]);
}
