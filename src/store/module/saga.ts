import { all, call, fork, put, takeLatest } from "redux-saga/effects";
import { fetchModulesFailure, fetchModulesRequest, fetchModulesSuccess, ModuleType } from "./redux";
import { ModuleApi } from "@/services/moduleAPI";

interface ModuleResponse {
    modules: ModuleType[];
    status: number;
    statusText: string;
}

function* fetchModule(): Generator<any, void, ModuleResponse> {
    try {
        const response = yield call(ModuleApi.fetch);
        yield put(fetchModulesSuccess(response))
    } catch (error: any) {
        yield put(fetchModulesFailure(error.message || 'Failed to fetch module'))
    }
}

function* initModule() {
    yield put(fetchModulesRequest());
}



export function* moduleSaga() {
    yield all([initModule(),
    takeLatest(fetchModulesRequest.type, fetchModule)
    ]);
}
