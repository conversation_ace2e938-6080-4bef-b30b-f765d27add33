import { combineReducers } from "@reduxjs/toolkit";

import authReducer from "./auth/redux";
import rolesReducer from "./roles/redux";
import adminUserReducer from "./adminUser/redux";
import membersReducer from "./members/redux";

const rootReducer = combineReducers({
  auth: authReducer,
  roles: rolesReducer,
  adminUser: adminUserReducer,
  members: membersReducer,
});

export default rootReducer;
