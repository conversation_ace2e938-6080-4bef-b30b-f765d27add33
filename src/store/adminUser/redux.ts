import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AdminUser, CreateAdminUserRequest, UpdateAdminUserRequest } from "@/types/adminUser";
import type { Permission } from "@/types/adminUser";

// Enhanced state interface with Map-based storage for better performance
interface AdminUserState {
  // Current user data
  user: AdminUser | null;
  loading: boolean;
  error: string | null;

  // Admin users management with Map for O(1) lookups
  adminUsersMap: Record<string, AdminUser>; // Map-like object for serialization
  adminUsersList: AdminUser[]; // Keep array for compatibility
  adminUsersLoading: boolean;
  adminUsersError: string | null;
  lastFetched: number | null; // For cache invalidation

  // CRUD operations loading states
  createLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;

  // UI state
  selectedAdminUser: AdminUser | null;

  // Statistics cache
  stats: {
    total: number;
    superAdmins: number;
    admins: number;
    moderators: number;
    activeUsers: number;
    newThisMonth: number;
  } | null;
}

const initialState: AdminUserState = {
  // Current user data
  user: null,
  loading: false,
  error: null,

  // Admin users management
  adminUsersMap: {},
  adminUsersList: [],
  adminUsersLoading: false,
  adminUsersError: null,
  lastFetched: null,

  // CRUD operations loading states
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,

  // UI state
  selectedAdminUser: null,

  // Statistics cache
  stats: null,
};

const adminUserSlice = createSlice({
  name: "adminUser",
  initialState,
  reducers: {
    fetchAdminUserRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchAdminUserSuccess(state, action: PayloadAction<AdminUser>) {
      state.loading = false;
      state.user = action.payload;
      state.error = null;
    },
    fetchAdminUserFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch Single Admin User by UUID
    fetchSingleAdminUserRequest(state, action: PayloadAction<string>) {
      state.loading = true;
      state.error = null;
    },
    fetchSingleAdminUserSuccess(state, action: PayloadAction<AdminUser>) {
      state.loading = false;
      state.error = null;

      // Add the user to both list and map if not already present
      const existingIndex = state.adminUsersList.findIndex(user => user.uuid === action.payload.uuid);
      if (existingIndex === -1) {
        state.adminUsersList.push(action.payload);
      } else {
        state.adminUsersList[existingIndex] = action.payload;
      }
      state.adminUsersMap[action.payload.uuid] = action.payload;

      // Set as selected user for the edit page
      state.selectedAdminUser = action.payload;
    },
    fetchSingleAdminUserFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    updateAdminUserPermissions(
      state,
      action: PayloadAction<{ id: string; permissions: Permission[] }>
    ) {
      if (state.user) {
        state.user.permissions = action.payload.permissions;
      }
    },
    clearAdminUserError(state) {
      state.error = null;
    },
    
    // Admin Users List Management
    fetchAdminUsersListRequest(state) {
      state.adminUsersLoading = true;
      state.adminUsersError = null;
    },
    fetchAdminUsersListSuccess(state, action: PayloadAction<AdminUser[]>) {
      state.adminUsersLoading = false;
      state.adminUsersList = action.payload;
      state.adminUsersError = null;
      state.lastFetched = Date.now();

      // Populate Map for O(1) lookups
      state.adminUsersMap = {};
      action.payload.forEach(user => {
        state.adminUsersMap[user.uuid] = user;
      });

      // Calculate and cache stats
      const users = action.payload;
      state.stats = {
        total: users.length,
        superAdmins: users.filter(user =>
          Array.isArray(user.roles) ? user.roles.includes('super_admin') : user.roles === 'super_admin'
        ).length,
        admins: users.filter(user =>
          Array.isArray(user.roles) ? user.roles.includes('admin') : user.roles === 'admin'
        ).length,
        moderators: users.filter(user =>
          Array.isArray(user.roles) ? user.roles.includes('moderator') : user.roles === 'moderator'
        ).length,
        activeUsers: users.filter(user => user.isactive).length,
        newThisMonth: 0, // Would need created date to calculate properly
      };
    },
    fetchAdminUsersListFailure(state, action: PayloadAction<string>) {
      state.adminUsersLoading = false;
      state.adminUsersError = action.payload;
    },

    // Create Admin User
    createAdminUserRequest(state, action: PayloadAction<CreateAdminUserRequest>) {
      state.createLoading = true;
      state.error = null;
    },
    createAdminUserSuccess(state, action: PayloadAction<AdminUser>) {
      state.createLoading = false;
      state.error = null;
      // Add the new user to both list and map
      state.adminUsersList.push(action.payload);
      state.adminUsersMap[action.payload.uuid] = action.payload;
      // Update stats
      if (state.stats) {
        state.stats.total += 1;
        const userRole = Array.isArray(action.payload.roles) ? action.payload.roles[0] : action.payload.roles;
        if (userRole === 'super_admin') state.stats.superAdmins += 1;
        else if (userRole === 'admin') state.stats.admins += 1;
        else if (userRole === 'moderator') state.stats.moderators += 1;
        if (action.payload.isactive) state.stats.activeUsers += 1;
      }
    },
    createAdminUserFailure(state, action: PayloadAction<string>) {
      state.createLoading = false;
      state.error = action.payload;
    },

    // Update Admin User
    updateAdminUserRequest(state, action: PayloadAction<{ uuid: string; data: any }>) {
      state.updateLoading = true;
      state.error = null;
    },
    updateAdminUserSuccess(state, action: PayloadAction<AdminUser>) {
      state.updateLoading = false;
      state.error = null;
      // Update the user in both list and map
      const index = state.adminUsersList.findIndex(user => user.uuid === action.payload.uuid);
      if (index !== -1) {
        state.adminUsersList[index] = action.payload;
      }
      state.adminUsersMap[action.payload.uuid] = action.payload;
      // Update current user if it's the same
      if (state.user?.uuid === action.payload.uuid) {
        state.user = action.payload;
      }
    },
    updateAdminUserFailure(state, action: PayloadAction<string>) {
      state.updateLoading = false;
      state.error = action.payload;
    },
    
    // Delete Admin User
    deleteAdminUserRequest(state, action: PayloadAction<string>) {
      state.deleteLoading = true;
      state.error = null;
    },
    deleteAdminUserSuccess(state, action: PayloadAction<string>) {
      state.deleteLoading = false;
      state.error = null;

      // Find the user being deleted to update stats
      const deletedUser = state.adminUsersMap[action.payload];

      // Remove the user from both list and map
      state.adminUsersList = state.adminUsersList.filter(user => user.uuid !== action.payload);
      delete state.adminUsersMap[action.payload];

      // Update stats
      if (state.stats && deletedUser) {
        state.stats.total -= 1;
        const userRole = Array.isArray(deletedUser.roles) ? deletedUser.roles[0] : deletedUser.roles;
        if (userRole === 'super_admin') state.stats.superAdmins -= 1;
        else if (userRole === 'admin') state.stats.admins -= 1;
        else if (userRole === 'moderator') state.stats.moderators -= 1;
        if (deletedUser.isactive) state.stats.activeUsers -= 1;
      }

      // Clear selected user if it was deleted
      if (state.selectedAdminUser?.uuid === action.payload) {
        state.selectedAdminUser = null;
      }
    },
    deleteAdminUserFailure(state, action: PayloadAction<string>) {
      state.deleteLoading = false;
      state.error = action.payload;
    },
    
    // Set Selected Admin User
    setSelectedAdminUser(state, action: PayloadAction<AdminUser | null>) {
      state.selectedAdminUser = action.payload;
    },
    
    // Clear Admin Users Error
    clearAdminUsersError(state) {
      state.adminUsersError = null;
    },

    // Cache management
    invalidateAdminUsersCache(state) {
      state.lastFetched = null;
      state.stats = null;
      state.adminUsersMap = {};
      state.adminUsersList = [];
    },
  },
});

export const {
  // Current user actions
  fetchAdminUserRequest,
  fetchAdminUserSuccess,
  fetchAdminUserFailure,
  clearAdminUserError,

  // Single Admin User by UUID
  fetchSingleAdminUserRequest,
  fetchSingleAdminUserSuccess,
  fetchSingleAdminUserFailure,

  // Admin Users List Management
  fetchAdminUsersListRequest,
  fetchAdminUsersListSuccess,
  fetchAdminUsersListFailure,

  // Create Admin User
  createAdminUserRequest,
  createAdminUserSuccess,
  createAdminUserFailure,

  // Update Admin User
  updateAdminUserRequest,
  updateAdminUserSuccess,
  updateAdminUserFailure,

  // Delete Admin User
  deleteAdminUserRequest,
  deleteAdminUserSuccess,
  deleteAdminUserFailure,

  // Utilities
  setSelectedAdminUser,
  clearAdminUsersError,
  invalidateAdminUsersCache,
} = adminUserSlice.actions;

// Helper function to check if cache should be refreshed (5 minutes)
export const shouldRefreshAdminUsers = (state: AdminUserState): boolean => {
  if (!state.lastFetched) return true;
  const fiveMinutes = 5 * 60 * 1000;
  return Date.now() - state.lastFetched > fiveMinutes;
};

export default adminUserSlice.reducer;
