import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AdminUser } from "@/types/adminUser";
import type { Permission } from "@/types/adminUser";

interface AdminUserState {
  user: AdminUser | null;
  loading: boolean;
  error: string | null;
  // Admin user management state
  adminUsersList: AdminUser[];
  adminUsersLoading: boolean;
  adminUsersError: string | null;
  updateLoading: boolean;
  deleteLoading: boolean;
  selectedAdminUser: AdminUser | null;
}

const initialState: AdminUserState = {
  user: null,
  loading: false,
  error: null,
  // Admin user management state
  adminUsersList: [],
  adminUsersLoading: false,
  adminUsersError: null,
  updateLoading: false,
  deleteLoading: false,
  selectedAdminUser: null,
};

const adminUserSlice = createSlice({
  name: "adminUser",
  initialState,
  reducers: {
    fetchAdminUserRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchAdminUserSuccess(state, action: PayloadAction<AdminUser>) {
      state.loading = false;
      state.user = action.payload;
      state.error = null;
    },
    fetchAdminUserFailure(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    updateAdminUserPermissions(
      state,
      action: PayloadAction<{ id: string; permissions: Permission[] }>
    ) {
      if (state.user) {
        state.user.permissions = action.payload.permissions;
      }
    },
    clearAdminUserError(state) {
      state.error = null;
    },
    
    // Admin Users List Management
    fetchAdminUsersListRequest(state) {
      state.adminUsersLoading = true;
      state.adminUsersError = null;
    },
    fetchAdminUsersListSuccess(state, action: PayloadAction<AdminUser[]>) {
      state.adminUsersLoading = false;
      state.adminUsersList = action.payload;
      state.adminUsersError = null;
    },
    fetchAdminUsersListFailure(state, action: PayloadAction<string>) {
      state.adminUsersLoading = false;
      state.adminUsersError = action.payload;
    },
    
    // Update Admin User
    updateAdminUserRequest(state, action: PayloadAction<{ uuid: string; data: any }>) {
      state.updateLoading = true;
      state.error = null;
    },
    updateAdminUserSuccess(state, action: PayloadAction<AdminUser>) {
      state.updateLoading = false;
      state.error = null;
      // Update the user in the list
      const index = state.adminUsersList.findIndex(user => user.uuid === action.payload.uuid);
      if (index !== -1) {
        state.adminUsersList[index] = action.payload;
      }
      // Update current user if it's the same
      if (state.user?.uuid === action.payload.uuid) {
        state.user = action.payload;
      }
    },
    updateAdminUserFailure(state, action: PayloadAction<string>) {
      state.updateLoading = false;
      state.error = action.payload;
    },
    
    // Delete Admin User
    deleteAdminUserRequest(state, action: PayloadAction<string>) {
      state.deleteLoading = true;
      state.error = null;
    },
    deleteAdminUserSuccess(state, action: PayloadAction<string>) {
      state.deleteLoading = false;
      state.error = null;
      // Remove the user from the list
      state.adminUsersList = state.adminUsersList.filter(user => user.uuid !== action.payload);
      // Clear selected user if it was deleted
      if (state.selectedAdminUser?.uuid === action.payload) {
        state.selectedAdminUser = null;
      }
    },
    deleteAdminUserFailure(state, action: PayloadAction<string>) {
      state.deleteLoading = false;
      state.error = action.payload;
    },
    
    // Set Selected Admin User
    setSelectedAdminUser(state, action: PayloadAction<AdminUser | null>) {
      state.selectedAdminUser = action.payload;
    },
    
    // Clear Admin Users Error
    clearAdminUsersError(state) {
      state.adminUsersError = null;
    },
  },
});

export const {
  fetchAdminUserRequest,
  fetchAdminUserSuccess,
  fetchAdminUserFailure,
  clearAdminUserError,
  // Admin Users List Management
  fetchAdminUsersListRequest,
  fetchAdminUsersListSuccess,
  fetchAdminUsersListFailure,
  // Update Admin User
  updateAdminUserRequest,
  updateAdminUserSuccess,
  updateAdminUserFailure,
  // Delete Admin User
  deleteAdminUserRequest,
  deleteAdminUserSuccess,
  deleteAdminUserFailure,
  // Utilities
  setSelectedAdminUser,
  clearAdminUsersError,
} = adminUserSlice.actions;

export default adminUserSlice.reducer;
