import { put, call, takeLatest, all, select } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { fetchCurrentAdminUser, updateAdminUser, deleteAdminUser, createAdminUser, CreateAdminUserResponse } from "@/services/adminAPI";
import { fetchAdminList } from "@/services/adminListAPI";
import {
  fetchAdminUserRequest,
  fetchAdminUserSuccess,
  fetchAdminUserFailure,
  fetchAdminUsersListRequest,
  fetchAdminUsersListSuccess,
  fetchAdminUsersListFailure,
  createAdminUserRequest,
  createAdminUserSuccess,
  createAdminUserFailure,
  updateAdminUserRequest,
  updateAdminUserSuccess,
  updateAdminUserFailure,
  deleteAdminUserRequest,
  deleteAdminUserSuccess,
  deleteAdminUserFailure,
  shouldRefreshAdminUsers,
} from "./redux";
import { AdminUser, AdminUserResponse, UpdateAdminUserResponse, CreateAdminUserRequest } from "@/types/adminUser";
import { RootState } from "../index";

function* fetchAdminUserSaga(): Generator<any, void, any> {
  try {
    const response: AdminUserResponse = yield call(fetchCurrentAdminUser);
    if (response && response.success && response.user) {
      yield put(fetchAdminUserSuccess(response.user));
    } else {
      yield put(
        fetchAdminUserFailure(response.message || "Failed to fetch admin user")
      );
    }
  } catch (error: any) {
    yield put(
      fetchAdminUserFailure(error.message || "Failed to fetch admin user")
    );
  }
}

function* fetchAdminUsersListSaga(): Generator<any, void, any> {
  try {
    // Check if we should use cached data
    const adminUserState: any = yield select((state: RootState) => state.adminUser);

    if (!shouldRefreshAdminUsers(adminUserState) && adminUserState.adminUsersList.length > 0) {
      // Use cached data, no need to fetch
      return;
    }

    const response: any = yield call(fetchAdminList);
    console.log(response,'responseresponse');
    if (response) {
      // Transform the response to match AdminUser interface
      const adminUsers = response.map((user: any) => ({
        ...user,
        uuid: user.uuid || user.id,
        // Ensure roles is always an array for consistency
        roles: Array.isArray(user.roles) ? user.roles : [user.roles],
        // Ensure required fields have defaults
        firstname: user.firstname || null,
        lastname: user.lastname || null,
        phone: user.phone || null,
        countrycode: user.countrycode || null,
        permissions: user.permissions || [],
      }));
      yield put(fetchAdminUsersListSuccess(adminUsers));
    } else {
      yield put(fetchAdminUsersListFailure("Failed to fetch admin users list"));
    }
  } catch (error: any) {
    yield put(
      fetchAdminUsersListFailure(error.message || "Failed to fetch admin users list")
    );
  }
}

function* createAdminUserSaga(
  action: PayloadAction<CreateAdminUserRequest>
): Generator<any, void, any> {
  try {
    const response: CreateAdminUserResponse = yield call(createAdminUser as any, action.payload);

    if (response && response.success && response.data) {
      // Transform the response to match AdminUser interface
      const newUser: AdminUser = {
        uuid: response.data.userId,
        username: response.data.username,
        email: response.data.email,
        firstname: null, // API doesn't return these fields
        lastname: null,
        phone: null,
        countrycode: null,
        isactive: true, // New users are active by default
        istemppassword: false,
        emailverified: false,
        roles: [response.data.role], // API returns single role
        createdby: response.data.createdBy,
        permissions: [], // Will be populated based on role
      };

      yield put(createAdminUserSuccess(newUser));
    } else {
      yield put(createAdminUserFailure(response.error || response.message || "Failed to create admin user"));
    }
  } catch (error: any) {
    yield put(createAdminUserFailure(error.message || "Failed to create admin user"));
  }
}

function* updateAdminUserSaga(
  action: PayloadAction<{ uuid: string; data: any }>
): Generator<any, void, any> {
  try {
    const { uuid, data } = action.payload;
    const response: UpdateAdminUserResponse = yield call(updateAdminUser, uuid, data);
    
    // Handle direct API response structure (the API returns the updated user object directly)
    if (response && response.uuid) {
      // Transform the response to match AdminUser interface
      const updatedUser = {
        uuid: response.uuid,
        username: response.username || '',
        email: response.email || '',
        firstname: response.firstname || null,
        lastname: response.lastname || null,
        phone: response.phone || null,
        countrycode: response.countrycode || null,
        isactive: response.isactive || false,
        istemppassword: response.istemppassword || false,
        emailverified: response.emailverified || false,
        roles: response.roles || [],
        createdby: response.createdby || '',
        permissions: [], // Add empty permissions array for compatibility
      };
      
      yield put(updateAdminUserSuccess(updatedUser as any));
    } else if (response && response.success === false) {
      // Handle error response
      yield put(updateAdminUserFailure(response.error || "Failed to update admin user"));
    } else {
      // Handle unexpected response format
      yield put(updateAdminUserFailure("Unexpected response format"));
    }
  } catch (error: any) {
    yield put(updateAdminUserFailure(error.message || "Failed to update admin user"));
  }
}

function* deleteAdminUserSaga(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const uuid = action.payload;
    const response: any = yield call(deleteAdminUser, uuid);
    
    if (response && response.success) {
      yield put(deleteAdminUserSuccess(uuid));
    } else {
      yield put(deleteAdminUserFailure(response.error || "Failed to delete admin user"));
    }
  } catch (error: any) {
    yield put(deleteAdminUserFailure(error.message || "Failed to delete admin user"));
  }
}

function* watchAdminUserOnStart() {
  yield put(fetchAdminUserRequest());
}

export function* adminUserSaga() {
  yield all([
    watchAdminUserOnStart(),
    takeLatest(fetchAdminUserRequest.type, fetchAdminUserSaga),
    takeLatest(fetchAdminUsersListRequest.type, fetchAdminUsersListSaga),
    takeLatest(createAdminUserRequest.type, createAdminUserSaga),
    takeLatest(updateAdminUserRequest.type, updateAdminUserSaga),
    takeLatest(deleteAdminUserRequest.type, deleteAdminUserSaga),
  ]);
}
