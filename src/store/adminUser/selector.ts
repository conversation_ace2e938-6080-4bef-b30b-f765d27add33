import { createSelector } from '@reduxjs/toolkit';
import { useAppSelector } from "../hooks";
import { RootState } from "..";
import { AdminUser } from "@/types/adminUser";

// Base selectors
export const selectAdminUserState = (state: RootState) => state.adminUser;
export const selectAdminUsersList = (state: RootState) => state.adminUser.adminUsersList;
export const selectAdminUsersMap = (state: RootState) => state.adminUser.adminUsersMap;
export const selectAdminUsersLoading = (state: RootState) => state.adminUser.adminUsersLoading;
export const selectAdminUsersError = (state: RootState) => state.adminUser.adminUsersError;
export const selectSelectedAdminUser = (state: RootState) => state.adminUser.selectedAdminUser;
export const selectAdminUsersStats = (state: RootState) => state.adminUser.stats;
export const selectCreateLoading = (state: RootState) => state.adminUser.createLoading;
export const selectUpdateLoading = (state: RootState) => state.adminUser.updateLoading;
export const selectDeleteLoading = (state: RootState) => state.adminUser.deleteLoading;
export const selectLastFetched = (state: RootState) => state.adminUser.lastFetched;

// O(1) lookup selectors using Map
export const selectAdminUserById = (state: RootState, uuid: string) =>
  state.adminUser.adminUsersMap[uuid] || null;

// Hook for getting admin user by ID
export const useAdminUserById = (uuid: string) =>
  useAppSelector(state => selectAdminUserById(state, uuid));

// Memoized selectors using Map for better performance
export const selectAdminUsersByRole = createSelector(
  [selectAdminUsersMap],
  (usersMap: Record<string, AdminUser>) => {
    const users = Object.values(usersMap);
    return users.reduce((acc, user) => {
      const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
      userRoles.forEach(role => {
        if (!acc[role]) acc[role] = [];
        acc[role].push(user);
      });
      return acc;
    }, {} as Record<string, AdminUser[]>);
  }
);

export const selectActiveAdminUsers = createSelector(
  [selectAdminUsersMap],
  (usersMap: Record<string, AdminUser>) =>
    Object.values(usersMap).filter(user => user.isactive)
);

// Selector to check if data should be refreshed
export const selectShouldRefreshAdminUsers = createSelector(
  [selectLastFetched, selectAdminUsersList],
  (lastFetched: number | null, usersList: AdminUser[]) => {
    if (!lastFetched || usersList.length === 0) return true;
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - lastFetched > fiveMinutes;
  }
);

// Utility functions for filtering and sorting (to be used in components)
export const filterAdminUsers = (
  users: AdminUser[],
  searchTerm: string = '',
  roleFilter: string = 'all'
): AdminUser[] => {
  let filtered = users;

  // Apply search filter
  if (searchTerm.trim()) {
    const search = searchTerm.toLowerCase();
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(search) ||
      user.email.toLowerCase().includes(search) ||
      (Array.isArray(user.roles) ? user.roles : [user.roles]).some(role =>
        role.toLowerCase().includes(search)
      )
    );
  }

  // Apply role filter
  if (roleFilter !== 'all') {
    filtered = filtered.filter(user => {
      const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];
      return userRoles.includes(roleFilter);
    });
  }

  return filtered;
};

export const sortAdminUsers = (
  users: AdminUser[],
  sortBy: string = 'username',
  sortOrder: 'asc' | 'desc' = 'asc'
): AdminUser[] => {
  return [...users].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (sortBy) {
      case 'username':
        aValue = a.username;
        bValue = b.username;
        break;
      case 'email':
        aValue = a.email;
        bValue = b.email;
        break;
      case 'roles':
        aValue = Array.isArray(a.roles) ? a.roles.join(', ') : a.roles;
        bValue = Array.isArray(b.roles) ? b.roles.join(', ') : b.roles;
        break;
      case 'firstname':
        aValue = a.firstname || '';
        bValue = b.firstname || '';
        break;
      case 'lastname':
        aValue = a.lastname || '';
        bValue = b.lastname || '';
        break;
      default:
        aValue = a.username;
        bValue = b.username;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortOrder === 'asc' ? comparison : -comparison;
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

export const paginateAdminUsers = (
  users: AdminUser[],
  page: number = 1,
  pageSize: number = 10
): { users: AdminUser[]; total: number; totalPages: number } => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedUsers = users.slice(startIndex, endIndex);

  return {
    users: paginatedUsers,
    total: users.length,
    totalPages: Math.ceil(users.length / pageSize),
  };
};

// Convenience hooks
export const useAdminUser = () => useAppSelector(selectAdminUserState);
export const useAdminUsersList = () => useAppSelector(selectAdminUsersList);
export const useAdminUsersStats = () => useAppSelector(selectAdminUsersStats);
export const useSelectedAdminUser = () => useAppSelector(selectSelectedAdminUser);

// Auth-related selectors
export const selectIsAdmin = (state: RootState) =>
  state.auth.user?.roles.some(role => role === 'admin') || false;

export const selectCurrentUserRole = (state: RootState) =>
  state.adminUser.user?.roles || 'viewer';

// Hook versions for components
export const useIsAdmin = () => useAppSelector(selectIsAdmin);
export const useCurrentUserRole = () => useAppSelector(selectCurrentUserRole);

