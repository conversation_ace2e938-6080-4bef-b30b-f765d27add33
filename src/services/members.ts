import apiService from '@/lib/apiService';
import {
  Member,
  MemberWithRelations,
  MemberSearchParams,
  MemberSearchFilters,
  MemberFilters,
  MemberBulkAction,
  MemberStats,
  VerificationRequest,
  OrganizationVerificationRequest,
  AwardApplication,
  FeatureFlagUpdate,
  MemberExportOptions,
  MemberDashboardStats,
} from '@/types/member';
import { 
  CreateMemberData, 
  UpdateMemberData, 
  MemberFormData,
  type OrganizationData,
  type NewOrganizationData,
} from '@/lib/validations/member';
import { PaginationParams, BaseFilters } from '@/types/api';

// Enhanced members service using the new API service
export const membersService = {
  // Get members with pagination and filters
  async getMembers(params: MemberSearchParams) {
    const pagination: PaginationParams = {
      page: params.page,
      pageSize: params.pageSize,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    };

    const filters: BaseFilters = {
      search: params.filters.search,
      status: params.filters.status,
      role: params.filters.role,
      membershipType: params.filters.membershipType,
      industry: params.filters.industry,
    };

    return apiService.getPaginated<MemberWithRelations>(
      '/members',
      pagination,
      filters,
      {
        cache: { ttl: 300, key: 'members-list' }, // Cache for 5 minutes
        retry: { maxRetries: 2 },
        context: 'Members List'
      }
    );
  },

  // Advanced search with comprehensive filters
  async searchMembers(filters: MemberSearchFilters, pagination?: PaginationParams) {
    const searchParams = {
      ...filters,
      ...pagination,
    };

    return apiService.getPaginated<MemberWithRelations>(
      '/members/search',
      pagination || { page: 1, pageSize: 25 },
      searchParams,
      {
        cache: { ttl: 300, key: 'members-search' },
        retry: { maxRetries: 2 },
        context: 'Advanced Member Search'
      }
    );
  },

  // Get member by ID with all relations
  async getMember(id: string) {
    return apiService.get<MemberWithRelations>(
      `/members/${id}`,
      undefined,
      {
        cache: { ttl: 600, key: `member-${id}` }, // Cache for 10 minutes
        context: 'Get Member'
      }
    );
  },

  // Create new member
  async createMember(memberData: CreateMemberData) {
    return apiService.post<Member>(
      '/members',
      memberData,
      {
        context: 'Create Member'
      }
    );
  },

  // Update member
  async updateMember(id: string, memberData: UpdateMemberData) {
    const result = await apiService.put<Member>(
      `/members/${id}`,
      memberData,
      {
        context: 'Update Member'
      }
    );

    // Clear member cache after update
    apiService.clearCache(`member-${id}`);
    apiService.clearCache('members-list');

    return result;
  },

  // Delete member
  async deleteMember(id: string) {
    const result = await apiService.delete<{ deleted: boolean }>(
      `/members/${id}`,
      {
        context: 'Delete Member'
      }
    );

    // Clear member cache after deletion
    apiService.clearCache(`member-${id}`);
    apiService.clearCache('members-list');

    return result;
  },

  // Bulk actions
  async bulkAction(params: MemberBulkAction) {
    return apiService.post<{ success: string[]; failed: string[]; message: string }>(
      '/members/bulk',
      params,
      {
        context: 'Bulk Action'
      }
    );
  },

  // Verification management
  async verifyMember(request: VerificationRequest) {
    return apiService.post<{ success: boolean; message: string }>(
      `/members/${request.memberId}/verify`,
      request,
      {
        context: 'Verify Member'
      }
    );
  },

  async verifyOrganization(request: OrganizationVerificationRequest) {
    return apiService.post<{ success: boolean; message: string }>(
      `/organizations/${request.organizationId}/verify`,
      request,
      {
        context: 'Verify Organization'
      }
    );
  },

  // Email verification
  async verifyEmail(memberId: number) {
    return apiService.post<{ success: boolean; message: string }>(
      `/members/${memberId}/verify-email`,
      {},
      {
        context: 'Verify Email'
      }
    );
  },

  // Status management
  async updateMemberStatus(memberId: number, status: string) {
    return apiService.patch<{ success: boolean; message: string }>(
      `/members/${memberId}/status`,
      { status },
      {
        context: 'Update Member Status'
      }
    );
  },

  // Award management
  async submitAwardApplication(application: AwardApplication) {
    return apiService.post<{ success: boolean; message: string }>(
      '/members/awards',
      application,
      {
        context: 'Submit Award Application'
      }
    );
  },

  // Feature flag management
  async updateFeatureFlag(update: FeatureFlagUpdate) {
    return apiService.patch<{ success: boolean; message: string }>(
      `/members/${update.memberId}/features/${update.featureHandle}`,
      { enabled: update.enabled },
      {
        context: 'Update Feature Flag'
      }
    );
  },

  // Get member statistics
  async getMemberStats() {
    return apiService.get<MemberStats>(
      '/members/stats',
      undefined,
      {
        cache: { ttl: 900, key: 'member-stats' }, // Cache for 15 minutes
        context: 'Member Stats'
      }
    );
  },

  // Get dashboard statistics
  async getDashboardStats() {
    return apiService.get<MemberDashboardStats>(
      '/members/dashboard-stats',
      undefined,
      {
        cache: { ttl: 900, key: 'dashboard-stats' }, // Cache for 15 minutes
        context: 'Dashboard Stats'
      }
    );
  },

  // Get industries list
  async getIndustries() {
    return apiService.get<string[]>(
      '/members/industries',
      undefined,
      {
        cache: { ttl: 3600, key: 'industries' }, // Cache for 1 hour
        context: 'Industries List'
      }
    );
  },

  // Get organizations for autocomplete
  async getOrganizations(search?: string) {
    const params = search ? { search } : {};
    return apiService.get<Array<{ id: number; name: string; city?: string; state?: string }>>(
      '/organizations',
      params,
      {
        cache: { ttl: 1800, key: 'organizations' }, // Cache for 30 minutes
        context: 'Organizations List'
      }
    );
  },

  // Create new organization
  async createOrganization(orgData: NewOrganizationData) {
    return apiService.post<OrganizationData>(
      '/organizations',
      orgData,
      {
        context: 'Create Organization'
      }
    );
  },

  // Update organization
  async updateOrganization(id: string, orgData: Partial<OrganizationData>) {
    const result = await apiService.put<OrganizationData>(
      `/organizations/${id}`,
      orgData,
      {
        context: 'Update Organization'
      }
    );

    // Clear organization cache after update
    apiService.clearCache('organizations');
    return result;
  },

  // Delete organization
  async deleteOrganization(id: string) {
    const result = await apiService.delete<{ deleted: boolean }>(
      `/organizations/${id}`,
      {
        context: 'Delete Organization'
      }
    );

    // Clear organization cache after deletion
    apiService.clearCache('organizations');
    return result;
  },

  // Export members
  async exportMembers(options: MemberExportOptions) {
    return apiService.post<{ downloadUrl: string; filename: string }>(
      '/members/export',
      options,
      {
        context: 'Export Members'
      }
    );
  },

  // Upload member photo
  async uploadMemberPhoto(memberId: string, file: File) {
    return apiService.uploadFile(
      `/members/${memberId}/photo`,
      file,
      {
        context: 'Upload Member Photo'
      }
    );
  },

  // Get member activity
  async getMemberActivity(memberId: string, limit: number = 10) {
    return apiService.get<any[]>(
      `/members/${memberId}/activity`,
      { limit },
      {
        cache: { ttl: 300, key: `member-activity-${memberId}` },
        context: 'Member Activity'
      }
    );
  },

  // Get member audit log
  async getMemberAuditLog(memberId: string, limit: number = 20) {
    return apiService.get<any[]>(
      `/members/${memberId}/audit-log`,
      { limit },
      {
        cache: { ttl: 300, key: `member-audit-${memberId}` },
        context: 'Member Audit Log'
      }
    );
  },

  // Clear all member-related cache
  clearCache() {
    apiService.clearCache('members');
    apiService.clearCache('member-');
    apiService.clearCache('industries');
    apiService.clearCache('organizations');
    apiService.clearCache('dashboard-stats');
  }
}; 