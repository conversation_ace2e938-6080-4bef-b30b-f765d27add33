import { getAuthToken, getCognitoTokensFromCookies } from "@/utils/auth";
import axios from "axios";
// import { createAxiosInstance } from "@/hooks/useFetch";


export const createAxiosInstance = (baseURL?: string) => {
  const instance = axios.create({
    baseURL: baseURL || process.env.NEXT_PUBLIC_API_URL || '',
    timeout: 20000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor for auth tokens and global config
  instance.interceptors.request.use(
    (config) => {
      // Add timestamp to prevent caching for GET requests
      if (config.method === 'get' && !config.params) {
        config.params = {};
      }
      if (config.method === 'get') {
        config.params._t = Date.now();
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for global error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // Global error handling logic can be added here
      return Promise.reject(error);
    }
  );

  return instance;
};

/**
 * Get the current user's access token for API calls
 */
function getAccessToken(): string | null {
  // Try to get token from cookies first (synced from Cognito)
  const cognitoTokens = getCognitoTokensFromCookies();
  if (cognitoTokens.accessToken) {
    return cognitoTokens.accessToken;
  }

  // Fallback to legacy token
  return getAuthToken();
}

// Create axios instance using the shared utility
export const apiClient = createAxiosInstance();

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  const token = getAccessToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});