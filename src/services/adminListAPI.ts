import { getAuthToken, getCognitoTokensFromCookies } from '@/utils/auth';
import { it } from 'node:test';

// Interface for the admin list API response
export interface AdminListUser {
  id: number;
  uuid: string;
  username: string;
  email: string;
  roles: string[];
  last_login: string | null;
  createdby: {
    uuid: string;
    username: string;
  };
  cognito_id: string;
  created_at: string;
  updated_at: string | null;
  updatedBy: string;
}

export interface AdminListResponse {
  status_code: number;
  success: boolean;
  message: string;
  admins: AdminListUser[];
}

// Simplified interface for table display
export interface AdminTableUser {
  uuid: string;
  username: string;
  email: string;
  roles: string[];
  last_login: string | null;
  phone?: string;
  countrycode?: string;
  firstname?: string | null;
  lastname?: string | null;
  createdby: {
    uuid: string;
    username: string;
  };
}

/**
 * Get the current user's access token for API calls
 */
function getAccessToken(): string | null {
  // Try to get token from cookies first (synced from Cognito)
  const cognitoTokens = getCognitoTokensFromCookies();
  if (cognitoTokens.accessToken) {
    return cognitoTokens.accessToken;
  }
  
  // Fallback to legacy token
  return getAuthToken();
}

/**
 * Fetch admin users list from the API
 */
export async function fetchAdminList(): Promise<AdminTableUser[]> {
  try {
    const accessToken = getAccessToken();
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/admin-list`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result: AdminListResponse = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch admin list');
    }
    
    // Transform the API response to match our table interface
    const transformedUsers = result.admins
    // .map(admin => ({
    //   id: admin.uuid, // Use UUID as the primary identifier
    //   username: admin.username,
    //   email: admin.email,
    //   roles: admin.roles,
    //   last_login: admin.last_login,
    //   createdBy: admin.createdby,
    // }));
    
    return transformedUsers;
    
  } catch (error: any) {
    console.error('Fetch admin list error:', error);
    throw new Error(error.message || 'Failed to fetch admin users list');
  }
}

/**
 * Get admin user statistics (simplified version)
 */
export async function getAdminStats(users: AdminTableUser[]) {
  const stats = {
    total: users.length,
    superAdmins: users.filter(user => user.roles.includes('super_admin')).length,
    admins: users.filter(user => user.roles.includes('admin')).length,
    moderators: users.filter(user => user.roles.includes('moderator')).length,
    activeUsers: users.filter(user => user.last_login !== null).length,
    newThisMonth: 0, // Would need created_at date to calculate this properly
  };
  
  return stats;
}

/**
 * Filter admin users based on search criteria
 */
export function filterAdminUsers(
  users: AdminTableUser[],
  searchTerm: string = '',
  roleFilter: string = 'all'
): AdminTableUser[] {
  let filtered = users;
  
  // Apply search filter
  if (searchTerm.trim()) {
    const search = searchTerm.toLowerCase();
    filtered = filtered.filter(user => 
      user.username.toLowerCase().includes(search) ||
      user.email.toLowerCase().includes(search) ||
      user.roles.some(role => role.toLowerCase().includes(search))
    );
  }
  
  // Apply role filter
  if (roleFilter !== 'all') {
    filtered = filtered.filter(user => user.roles.includes(roleFilter));
  }
  
  return filtered;
}

/**
 * Sort admin users by specified field
 */
export function sortAdminUsers(
  users: AdminTableUser[],
  sortBy: string = 'username',
  sortOrder: 'asc' | 'desc' = 'asc'
): AdminTableUser[] {
  return [...users].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case 'username':
        aValue = a.username;
        bValue = b.username;
        break;
      case 'email':
        aValue = a.email;
        bValue = b.email;
        break;
      case 'roles':
        aValue = a.roles.join(', ');
        bValue = b.roles.join(', ');
        break;
      case 'last_login':
        aValue = a.last_login || '';
        bValue = b.last_login || '';
        break;
      default:
        aValue = a.username;
        bValue = b.username;
    }
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortOrder === 'asc' ? comparison : -comparison;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Paginate admin users
 */
export function paginateAdminUsers(
  users: AdminTableUser[],
  page: number = 1,
  pageSize: number = 10
): { users: AdminTableUser[]; total: number; totalPages: number } {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedUsers = users.slice(startIndex, endIndex);
  
  return {
    users: paginatedUsers,
    total: users.length,
    totalPages: Math.ceil(users.length / pageSize),
  };
}
