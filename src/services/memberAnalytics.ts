import {
  MemberGrowthAnalytics,
  MembershipTierAnalytics,
  CommunityStatusAnalytics,
  VerificationStatusAnalytics,
  BlacklistAnalytics,
  FeatureFlagAnalytics,
  ABTestingAnalytics,
  AnalyticsDashboardSummary,
  AnalyticsExportOptions,
  AnalyticsExportResult,
  BlacklistReportData,
  FeatureFlagData,
} from '@/types/analytics';

// Mock data for development
const mockMemberGrowthData: MemberGrowthAnalytics = {
  timeSeries: [
    { date: '2024-01', value: 1250 },
    { date: '2024-02', value: 1320 },
    { date: '2024-03', value: 1410 },
    { date: '2024-04', value: 1480 },
    { date: '2024-05', value: 1560 },
    { date: '2024-06', value: 1620 },
  ],
  monthlyGrowth: [
    { period: 'Jan 2024', newMembers: 45, totalMembers: 1250, growthRate: 3.7, churnRate: 0.8 },
    { period: 'Feb 2024', newMembers: 52, totalMembers: 1320, growthRate: 4.2, churnRate: 0.9 },
    { period: 'Mar 2024', newMembers: 48, totalMembers: 1410, growthRate: 3.5, churnRate: 0.7 },
    { period: 'Apr 2024', newMembers: 55, totalMembers: 1480, growthRate: 4.0, churnRate: 0.8 },
    { period: 'May 2024', newMembers: 62, totalMembers: 1560, growthRate: 4.2, churnRate: 0.9 },
    { period: 'Jun 2024', newMembers: 58, totalMembers: 1620, growthRate: 3.7, churnRate: 0.8 },
  ],
  yearlyGrowth: [
    { period: '2023', newMembers: 580, totalMembers: 1200, growthRate: 93.5, churnRate: 8.2 },
    { period: '2024', newMembers: 320, totalMembers: 1620, growthRate: 35.0, churnRate: 4.1 },
  ],
  growthTrend: 'increasing',
  averageGrowthRate: 3.9,
};

const mockMembershipTierData: MembershipTierAnalytics = {
  distribution: [
    { tier: 'basic', count: 850, percentage: 52.5, revenue: 85000, averageLifetime: 18 },
    { tier: 'premium', count: 520, percentage: 32.1, revenue: 156000, averageLifetime: 24 },
    { tier: 'enterprise', count: 180, percentage: 11.1, revenue: 270000, averageLifetime: 36 },
    { tier: 'vip', count: 70, percentage: 4.3, revenue: 140000, averageLifetime: 48 },
  ],
  totalRevenue: 651000,
  averageRevenuePerMember: 401.85,
  tierConversionRates: {
    basicToPremium: 12.5,
    premiumToEnterprise: 8.2,
    enterpriseToVip: 3.1,
  },
};

const mockCommunityStatusData: CommunityStatusAnalytics = {
  statusBreakdown: [
    { status: 'verified', count: 1250, percentage: 77.2, averageVerificationTime: 2.5 },
    { status: 'unverified', count: 280, percentage: 17.3, averageVerificationTime: 0 },
    { status: 'pending', count: 65, percentage: 4.0, averageVerificationTime: 5.2 },
    { status: 'rejected', count: 25, percentage: 1.5, averageVerificationTime: 1.8 },
  ],
  verificationMetrics: {
    totalVerified: 1250,
    verificationRate: 77.2,
    averageVerificationTime: 2.5,
    pendingVerifications: 65,
  },
};

const mockVerificationStatusData: VerificationStatusAnalytics = {
  statusBreakdown: [
    { status: 'completed', count: 1250, percentage: 77.2, averageProcessingTime: 2.5 },
    { status: 'in_progress', count: 65, percentage: 4.0, averageProcessingTime: 5.2 },
    { status: 'requires_review', count: 280, percentage: 17.3, averageProcessingTime: 0 },
    { status: 'rejected', count: 25, percentage: 1.5, averageProcessingTime: 1.8 },
  ],
  processingMetrics: {
    totalCompleted: 1250,
    completionRate: 77.2,
    averageProcessingTime: 2.5,
    backlogSize: 345,
  },
};

const mockBlacklistData: BlacklistAnalytics = {
  statistics: {
    totalReports: 45,
    activeReports: 12,
    resolvedReports: 28,
    falsePositives: 5,
    spamReports: 20,
    disposableEmailReports: 15,
    suspiciousActivityReports: 8,
    manualFlags: 2,
  },
  reportsByType: [
    { label: 'Spam', value: 20, percentage: 44.4 },
    { label: 'Disposable Email', value: 15, percentage: 33.3 },
    { label: 'Suspicious Activity', value: 8, percentage: 17.8 },
    { label: 'Manual Flag', value: 2, percentage: 4.4 },
  ],
  reportsBySeverity: [
    { label: 'Low', value: 15, percentage: 33.3 },
    { label: 'Medium', value: 18, percentage: 40.0 },
    { label: 'High', value: 10, percentage: 22.2 },
    { label: 'Critical', value: 2, percentage: 4.4 },
  ],
  reportsByStatus: [
    { label: 'Active', value: 12, percentage: 26.7 },
    { label: 'Resolved', value: 28, percentage: 62.2 },
    { label: 'False Positive', value: 5, percentage: 11.1 },
  ],
  timeSeriesData: [
    { date: '2024-01', value: 3 },
    { date: '2024-02', value: 5 },
    { date: '2024-03', value: 8 },
    { date: '2024-04', value: 12 },
    { date: '2024-05', value: 10 },
    { date: '2024-06', value: 7 },
  ],
  riskAssessment: {
    overallRisk: 'medium',
    riskScore: 6.2,
    riskFactors: [
      'High spam detection rate',
      'Multiple disposable email reports',
      'Recent suspicious activity increase',
    ],
    recommendations: [
      'Implement stricter email validation',
      'Add CAPTCHA for new registrations',
      'Review and update spam detection rules',
    ],
  },
};

const mockFeatureFlagData: FeatureFlagAnalytics = {
  features: [
    {
      featureHandle: 'advanced_search',
      featureName: 'Advanced Search',
      enabledCount: 1200,
      totalCount: 1620,
      adoptionRate: 74.1,
      usageByTier: [
        { tier: 'basic', enabledCount: 600, totalCount: 850, adoptionRate: 70.6 },
        { tier: 'premium', enabledCount: 420, totalCount: 520, adoptionRate: 80.8 },
        { tier: 'enterprise', enabledCount: 150, totalCount: 180, adoptionRate: 83.3 },
        { tier: 'vip', enabledCount: 30, totalCount: 70, adoptionRate: 42.9 },
      ],
    },
    {
      featureHandle: 'analytics_dashboard',
      featureName: 'Analytics Dashboard',
      enabledCount: 980,
      totalCount: 1620,
      adoptionRate: 60.5,
      usageByTier: [
        { tier: 'basic', enabledCount: 400, totalCount: 850, adoptionRate: 47.1 },
        { tier: 'premium', enabledCount: 380, totalCount: 520, adoptionRate: 73.1 },
        { tier: 'enterprise', enabledCount: 160, totalCount: 180, adoptionRate: 88.9 },
        { tier: 'vip', enabledCount: 40, totalCount: 70, adoptionRate: 57.1 },
      ],
    },
    {
      featureHandle: 'bulk_operations',
      featureName: 'Bulk Operations',
      enabledCount: 650,
      totalCount: 1620,
      adoptionRate: 40.1,
      usageByTier: [
        { tier: 'basic', enabledCount: 200, totalCount: 850, adoptionRate: 23.5 },
        { tier: 'premium', enabledCount: 250, totalCount: 520, adoptionRate: 48.1 },
        { tier: 'enterprise', enabledCount: 150, totalCount: 180, adoptionRate: 83.3 },
        { tier: 'vip', enabledCount: 50, totalCount: 70, adoptionRate: 71.4 },
      ],
    },
  ],
  overallAdoptionRate: 58.2,
  adoptionByTier: [
    { label: 'Basic', value: 47.1, percentage: 47.1 },
    { label: 'Premium', value: 67.3, percentage: 67.3 },
    { label: 'Enterprise', value: 85.2, percentage: 85.2 },
    { label: 'VIP', value: 57.1, percentage: 57.1 },
  ],
  featurePerformance: [
    {
      featureHandle: 'advanced_search',
      featureName: 'Advanced Search',
      performance: 'excellent',
      metrics: { engagement: 85, retention: 92, satisfaction: 88 },
    },
    {
      featureHandle: 'analytics_dashboard',
      featureName: 'Analytics Dashboard',
      performance: 'good',
      metrics: { engagement: 72, retention: 78, satisfaction: 75 },
    },
    {
      featureHandle: 'bulk_operations',
      featureName: 'Bulk Operations',
      performance: 'average',
      metrics: { engagement: 58, retention: 65, satisfaction: 62 },
    },
  ],
};

const mockABTestingData: ABTestingAnalytics = {
  activeTests: [
    {
      testId: 'search_ui_v2',
      testName: 'Search UI Version 2',
      variant: 'control',
      participants: 800,
      conversions: 120,
      conversionRate: 15.0,
      confidence: 95.2,
      isSignificant: true,
    },
    {
      testId: 'search_ui_v2',
      testName: 'Search UI Version 2',
      variant: 'treatment',
      participants: 820,
      conversions: 145,
      conversionRate: 17.7,
      confidence: 95.2,
      isSignificant: true,
    },
  ],
  completedTests: [
    {
      testId: 'welcome_email_v2',
      testName: 'Welcome Email Version 2',
      variant: 'control',
      participants: 500,
      conversions: 75,
      conversionRate: 15.0,
      confidence: 92.1,
      isSignificant: false,
    },
    {
      testId: 'welcome_email_v2',
      testName: 'Welcome Email Version 2',
      variant: 'treatment',
      participants: 520,
      conversions: 78,
      conversionRate: 15.0,
      confidence: 92.1,
      isSignificant: false,
    },
  ],
  overallResults: {
    totalTests: 8,
    significantResults: 5,
    averageLift: 12.5,
  },
};

class MemberAnalyticsService {
  private baseUrl = '/api/analytics';

  // Member Growth Analytics
  async getMemberGrowthAnalytics(dateRange?: { start: string; end: string }): Promise<MemberGrowthAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/member-growth${dateRange ? `?start=${dateRange.start}&end=${dateRange.end}` : ''}`);
      if (!response.ok) throw new Error('Failed to fetch member growth analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for member growth analytics:', error);
      return mockMemberGrowthData;
    }
  }

  // Membership Tier Analytics
  async getMembershipTierAnalytics(): Promise<MembershipTierAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/membership-tiers`);
      if (!response.ok) throw new Error('Failed to fetch membership tier analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for membership tier analytics:', error);
      return mockMembershipTierData;
    }
  }

  // Community Status Analytics
  async getCommunityStatusAnalytics(): Promise<CommunityStatusAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/community-status`);
      if (!response.ok) throw new Error('Failed to fetch community status analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for community status analytics:', error);
      return mockCommunityStatusData;
    }
  }

  // Verification Status Analytics
  async getVerificationStatusAnalytics(): Promise<VerificationStatusAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/verification-status`);
      if (!response.ok) throw new Error('Failed to fetch verification status analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for verification status analytics:', error);
      return mockVerificationStatusData;
    }
  }

  // Blacklist Report Analytics
  async getBlacklistAnalytics(dateRange?: { start: string; end: string }): Promise<BlacklistAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/blacklist-reports${dateRange ? `?start=${dateRange.start}&end=${dateRange.end}` : ''}`);
      if (!response.ok) throw new Error('Failed to fetch blacklist analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for blacklist analytics:', error);
      return mockBlacklistData;
    }
  }

  // Feature Flag Analytics
  async getFeatureFlagAnalytics(): Promise<FeatureFlagAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/feature-flags`);
      if (!response.ok) throw new Error('Failed to fetch feature flag analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for feature flag analytics:', error);
      return mockFeatureFlagData;
    }
  }

  // A/B Testing Analytics
  async getABTestingAnalytics(): Promise<ABTestingAnalytics> {
    try {
      const response = await fetch(`${this.baseUrl}/ab-testing`);
      if (!response.ok) throw new Error('Failed to fetch A/B testing analytics');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for A/B testing analytics:', error);
      return mockABTestingData;
    }
  }

  // Get all analytics dashboard data
  async getAnalyticsDashboard(): Promise<AnalyticsDashboardSummary> {
    try {
      const response = await fetch(`${this.baseUrl}/dashboard`);
      if (!response.ok) throw new Error('Failed to fetch analytics dashboard');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for analytics dashboard:', error);
      return {
        memberGrowth: mockMemberGrowthData,
        membershipTiers: mockMembershipTierData,
        communityStatus: mockCommunityStatusData,
        verificationStatus: mockVerificationStatusData,
        blacklistReports: mockBlacklistData,
        featureFlags: mockFeatureFlagData,
        abTesting: mockABTestingData,
        lastUpdated: new Date().toISOString(),
      };
    }
  }

  // Get blacklist reports
  async getBlacklistReports(filters?: Record<string, any>): Promise<BlacklistReportData[]> {
    try {
      const queryParams = filters ? `?${new URLSearchParams(filters).toString()}` : '';
      const response = await fetch(`${this.baseUrl}/blacklist-reports/list${queryParams}`);
      if (!response.ok) throw new Error('Failed to fetch blacklist reports');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for blacklist reports:', error);
      return [
        {
          reportId: 'BL001',
          memberId: 123,
          memberName: 'John Doe',
          reportType: 'spam',
          severity: 'high',
          status: 'active',
          createdAt: '2024-06-15T10:30:00Z',
          description: 'Multiple spam emails detected',
          evidence: ['Email content analysis', 'User behavior patterns'],
        },
        {
          reportId: 'BL002',
          memberId: 456,
          memberName: 'Jane Smith',
          reportType: 'disposable_email',
          severity: 'medium',
          status: 'resolved',
          createdAt: '2024-06-10T14:20:00Z',
          resolvedAt: '2024-06-12T09:15:00Z',
          description: 'Disposable email address detected',
          evidence: ['Email domain analysis'],
        },
      ];
    }
  }

  // Get feature flag data
  async getFeatureFlagData(): Promise<FeatureFlagData[]> {
    try {
      const response = await fetch(`${this.baseUrl}/feature-flags/list`);
      if (!response.ok) throw new Error('Failed to fetch feature flag data');
      return await response.json();
    } catch (error) {
      console.warn('Using mock data for feature flag data:', error);
      return mockFeatureFlagData.features;
    }
  }

  // Export analytics data
  async exportAnalyticsData(options: AnalyticsExportOptions): Promise<AnalyticsExportResult> {
    try {
      const response = await fetch(`${this.baseUrl}/export`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(options),
      });
      if (!response.ok) throw new Error('Failed to export analytics data');
      return await response.json();
    } catch (error) {
      console.warn('Using mock export result:', error);
      return {
        filename: `analytics-export-${new Date().toISOString().split('T')[0]}.${options.format}`,
        downloadUrl: '#',
        size: 1024 * 1024, // 1MB
        format: options.format,
      };
    }
  }

  // Update blacklist report status
  async updateBlacklistReportStatus(reportId: string, status: 'active' | 'resolved' | 'false_positive'): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/blacklist-reports/${reportId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      });
      if (!response.ok) throw new Error('Failed to update blacklist report status');
    } catch (error) {
      console.error('Error updating blacklist report status:', error);
      throw error;
    }
  }

  // Toggle feature flag
  async toggleFeatureFlag(featureHandle: string, enabled: boolean): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/feature-flags/${featureHandle}/toggle`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled }),
      });
      if (!response.ok) throw new Error('Failed to toggle feature flag');
    } catch (error) {
      console.error('Error toggling feature flag:', error);
      throw error;
    }
  }
}

export const memberAnalyticsService = new MemberAnalyticsService(); 