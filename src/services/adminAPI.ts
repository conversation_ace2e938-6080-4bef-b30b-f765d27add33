import { getAuthToken, getCognitoTokensFromCookies } from "@/utils/auth";

export interface CreateAdminUserRequest {
  email: string;
  username: string;
  password: string;
  roles?: ("super_admin" | "admin" | "moderator")[];
}

export interface CreateAdminUserResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string;
  data?: {
    userId: string;
    username: string;
    email: string;
    role: string;
    status: string;
    createdBy: string;
    createdAt: string;
  };
}

/**
 * Get the current user's access token for API calls
 */
function getAccessToken(): string | null {
  // Try to get token from cookies first (synced from Cognito)
  const cognitoTokens = getCognitoTokensFromCookies();
  if (cognitoTokens.accessToken) {
    return cognitoTokens.accessToken;
  }

  // Fallback to legacy token
  return getAuthToken();
}

/**
 * Create a new admin user
 */
export async function createAdminUser(
  userData: CreateAdminUserRequest
): Promise<CreateAdminUserResponse> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("No access token found. Please log in again.");
    }
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/register`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(userData),
      }
    );

    const result: CreateAdminUserResponse = await response.json();

    if (!response.ok) {
      throw new Error(result.error || `HTTP error! status: ${response.status}`);
    }

    return result;
  } catch (error: any) {
    console.error("Create admin user error:", error);

    return {
      success: false,
      error: error.message || "Failed to create admin user",
      details: error.details || "An unexpected error occurred",
    };
  }
}

/**
 * Get list of admin users (placeholder for future implementation)
 */
export async function getAdminUsers(): Promise<any> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("No access token found. Please log in again.");
    }

    const response = await fetch("/api/admin/users", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error: any) {
    console.error("Get admin users error:", error);
    throw error;
  }
}

/**
 * Update an admin user using the /api/admin/{uuid} endpoint
 */
export async function updateAdminUser(
  uuid: string,
  userData: {
    password?: string;
    email?: string;
    firstname?: string;
    lastname?: string;
    phone?: string;
    countrycode?: string;
    isactive?: boolean;
    istemppassword?: boolean;
    roles?: string[];
  }
): Promise<any> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("No access token found. Please log in again.");
    }

    // Only include the allowed fields in the request body
    const requestBody = {
      ...(userData.password && { password: userData.password }),
      ...(userData.email && { email: userData.email }),
      ...(userData.firstname && { firstname: userData.firstname }),
      ...(userData.lastname && { lastname: userData.lastname }),
      ...(userData.phone && { phone: userData.phone }),
      ...(userData.countrycode && { countrycode: userData.countrycode }),
      ...(userData.isactive !== undefined && { isactive: userData.isactive }),
      ...(userData.istemppassword !== undefined && { istemppassword: userData.istemppassword }),
      ...(userData.roles && { roles: userData.roles }),
    };

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/${uuid}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Return the API response directly as it matches the expected structure
    return result;
  } catch (error: any) {
    console.error("Update admin user error:", error);
    return {
      success: false,
      error: error.message || "Failed to update admin user",
    };
  }
}

/**
 * Delete an admin user using the /api/admin/{uuid} endpoint
 */
export async function deleteAdminUser(uuid: string): Promise<any> {
  try {
    const accessToken = getAccessToken();

    if (!accessToken) {
      throw new Error("No access token found. Please log in again.");
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/${uuid}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result,
      message: "Admin user deleted successfully",
    };
  } catch (error: any) {
    console.error("Delete admin user error:", error);
    return {
      success: false,
      error: error.message || "Failed to delete admin user",
    };
  }
}

/**
 * Fetch the current admin user
 */
export async function fetchCurrentAdminUser(): Promise<any> {
  try {
    const accessToken = getAccessToken();
    if (!accessToken) {
      throw new Error("No access token found. Please log in again.");
    }
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/get-admin-user`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error: any) {
    console.error("Fetch current admin user error:", error);
    throw error;
  }
}
