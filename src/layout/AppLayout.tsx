'use client';

import { removeAuthToken } from '@/utils/auth';
import {
  AdminPanelSettings,
  Analytics,
  Business,
  Dashboard,
  Event,
  Lock,
  Logout,
  Menu as MenuIcon,
  People,
  Security,
  Settings,
} from '@mui/icons-material';
import {
  AppBar,
  Box,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  SxProps,
  Toolbar,
  Typography,
  useTheme
} from '@mui/material';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import ApiStatusIndicator from './ApiStatusIndicator';
import { useCurrentUserRole } from '@/store/adminUser/selector';

const drawerWidth = 240;

interface MenuItem {
  text: string;
  icon: React.ReactNode;
  path: string;
  role?: 'super_admin' | 'admin' | 'moderator';
}

interface AppLayoutProps {
  children: React.ReactNode;
  sx?: SxProps;
}

const menuItems: MenuItem[] = [
  { text: 'Dashboard', icon: <Dashboard />, path: '/dashboard' },
  { text: 'Members', icon: <People />, path: '/members' },
  { text: 'Admin Users', icon: <AdminPanelSettings />, path: '/admin-users', role: 'admin' },
  { text: 'Roles', icon: <Security />, path: '/roles', role: 'admin' },
];

function NavigationDrawer({ filteredMenuItems, handleNavigation, handleLogout }: {
  filteredMenuItems: MenuItem[];
  handleNavigation: (path: string) => void;
  handleLogout: () => void;
}) {
  return (
    <Box>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Image
          src="/CO-gold.svg"
          alt="US Chamber CO Logo"
          width={120}
          height={40}
          style={{ objectFit: 'contain' }}
        />
      </Box>
      <List>
        {filteredMenuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton onClick={() => handleNavigation(item.path)}>
              <ListItemIcon sx={{ color: '#1e3a8a' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
        <ListItem disablePadding>
          <ListItemButton onClick={handleLogout}>
            <ListItemIcon sx={{ color: '#dc2626' }}>
              <Logout />
            </ListItemIcon>
            <ListItemText primary="Logout" sx={{ color: '#dc2626' }} />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );
}

function AppToolbar({ mounted, handleDrawerToggle }: {
  mounted: boolean;
  handleDrawerToggle: () => void;
}) {
  return (
    <AppBar
      position="fixed"
      sx={{
        width: { md: `calc(100% - ${drawerWidth}px)` },
        ml: { md: `${drawerWidth}px` },
        backgroundColor: '#ffffff !important',
        color: '#1e3a8a !important',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        '& .MuiToolbar-root': {
          backgroundColor: '#ffffff',
          color: '#1e3a8a',
        },
      }}
      color="default"
    >
      <Toolbar sx={{ backgroundColor: '#ffffff', color: '#1e3a8a' }}>
        {mounted && (
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              display: { md: 'none' },
              color: '#1e3a8a',
            }}
          >
            <MenuIcon />
          </IconButton>
        )}
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{
            color: '#1e3a8a !important',
            fontWeight: 700,
            fontSize: { xs: '1.1rem', sm: '1.25rem' },
            textShadow: '0 1px 2px rgba(0,0,0,0.1)',
          }}
        >
          US Chamber CO - Member Dashboard
        </Typography>
      </Toolbar>
    </AppBar>
  );
}

export default function AppLayout({ children, sx }: AppLayoutProps) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const theme = useTheme();
  const router = useRouter();

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = useCurrentUserRole()
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleNavigation = (path: string) => {
    router.push(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleLogout = () => {
    router.push('/');
    removeAuthToken();
  };

  // Filter menu items based on user role
  const filteredMenuItems = menuItems.filter(item => {
    if (!item.role) return true;
    return item.role === currentUserRole;
  });

  return (
    <Box sx={{ display: 'flex', ...sx }}>
      <AppToolbar mounted={mounted} handleDrawerToggle={handleDrawerToggle} />
      <Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}>
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          <NavigationDrawer filteredMenuItems={filteredMenuItems} handleNavigation={handleNavigation} handleLogout={handleLogout} />
        </Drawer>
        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          <NavigationDrawer filteredMenuItems={filteredMenuItems} handleNavigation={handleNavigation} handleLogout={handleLogout} />
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, md: 3 },
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: { xs: 7, md: 8 },
          minHeight: '100vh',
          backgroundColor: '#f8fafc',
        }}
      >
        {children}
      </Box>
      {mounted && <ApiStatusIndicator />}
    </Box>
  );
}