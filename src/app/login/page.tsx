"use client";

import {
  <PERSON><PERSON>r<PERSON>oundary,
  LoginForm,
  LoginLayout,
  MFAForm,
  RegistrationProcessing,
  TOTPSetupForm,
} from "@/components/auth";
import "@/config/cognito";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  loginRequest,
  clearError,
  confirmMFARequest,
  setupTOTPRequest,
} from "@/store/auth/redux";
import { Alert } from "@mui/material";
import React, { useState } from "react";

export default function LoginPage() {
  const dispatch = useAppDispatch();
  const auth = useAppSelector((state) => state.auth);
  const [loginData, setLoginData] = useState({ username: "", password: "" });
  const [mfaData, setMfaData] = useState({ code: "" });

  // Handlers
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(clearError());
    dispatch(loginRequest(loginData));
  };

  // TODO: Implement MFA/TOTP submit handlers (dispatch saga actions)
  const handleMfaSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(clearError());
    dispatch(
      confirmMFARequest({ username: loginData.username, code: mfaData.code })
    );
    setMfaData({ code: "" });
  };
  const handleTotpSetupSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(clearError());
    dispatch(setupTOTPRequest({ code: mfaData.code }));
    setMfaData({ code: "" });
  };

  // Get page title and subtitle based on current state
  const getPageTitle = () => "Welcome to CO";

  const getPageSubtitle = () => {
    if (auth.totpSetup.required) {
      return "Set up Two-Factor Authentication";
    } else if (auth.mfaRequired) {
      return auth.mfaType === "TOTP"
        ? "Enter your Authenticator App code"
        : "Enter the SMS code sent to your phone";
    } else {
      return "Member Dashboard Login";
    }
  };

  return (
    <ErrorBoundary>
      <LoginLayout title={getPageTitle()} subtitle={getPageSubtitle()}>
        {auth.error && (
          <Alert severity="error" sx={{ mb: 2, width: "100%" }}>
            {auth.error}
          </Alert>
        )}
        {auth.totpSetup.required ? (
          <TOTPSetupForm
            sharedSecret={auth.totpSetup.sharedSecret || ""}
            username={loginData.username}
            mfaData={mfaData}
            setMfaData={setMfaData}
            onSubmit={handleTotpSetupSubmit}
            isLoading={auth.isLoading}
          />
        ) : auth.mfaRequired ? (
          <MFAForm
            mfaData={mfaData}
            setMfaData={setMfaData}
            onSubmit={handleMfaSubmit}
            isLoading={auth.isLoading}
          />
        ) : (
          <LoginForm
            loginData={loginData}
            setLoginData={setLoginData}
            onSubmit={handleLogin}
            isLoading={auth.isLoading}
          />
        )}
      </LoginLayout>
    </ErrorBoundary>
  );
}
