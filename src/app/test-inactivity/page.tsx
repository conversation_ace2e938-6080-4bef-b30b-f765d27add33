'use client';

import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  Chip,
  Grid,
  Card,
  CardContent,
  LinearProgress
} from '@mui/material';
import {
  Timer as TimerIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import AppLayout from '@/layout/AppLayout';
import { useUserInactivityLogout } from '@/hooks/useUserInactivityLogout';

/**
 * Test page to demonstrate the inactivity logout functionality
 * This page shows real-time information about user activity tracking
 */
export default function TestInactivityPage() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showTestWarning, setShowTestWarning] = useState(false);

  // Set up inactivity tracking with shorter timeout for testing (1 minute)
  const inactivityControls = useUserInactivityLogout({
    timeout: 1, // 1 minute for testing
    enabled: true,
    warningTime: 0.5, // 30 seconds warning
    checkInterval: 5000, // Check every 5 seconds
    onWarning: (timeLeft) => {
      setShowTestWarning(true);
      console.log('Test warning triggered:', timeLeft, 'minutes left');
    }
  });

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimeUntilLogout = (): number => {
    return inactivityControls.getTimeUntilLogout();
  };

  const getTimeSinceLastActivity = (): number => {
    return Date.now() - inactivityControls.getLastActivity();
  };

  const getProgressPercentage = (): number => {
    const timeUntilLogout = getTimeUntilLogout();
    const totalTimeout = 1 * 60 * 1000; // 1 minute in ms
    return Math.max(0, (timeUntilLogout / totalTimeout) * 100);
  };

  const handleResetActivity = () => {
    inactivityControls.resetActivity();
    setShowTestWarning(false);
  };

  const handleCheckInactivity = () => {
    inactivityControls.checkInactivity();
  };

  const handleDebugStatus = () => {
    if (inactivityControls.getDebugStatus) {
      const status = inactivityControls.getDebugStatus();
      console.log('🐛 Debug Status:', status);
      alert(JSON.stringify(status, null, 2));
    }
  };

  return (
    <AppLayout>
      <Container maxWidth="lg">
        <Box sx={{ py: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SecurityIcon color="primary" />
            Inactivity Logout Test
          </Typography>

          <Typography variant="body1" color="text.secondary" paragraph>
            This page demonstrates the automatic logout functionality when users are inactive for more than 30 minutes.
            For testing purposes, this page uses a 1-minute timeout instead of 30 minutes.
          </Typography>

          {showTestWarning && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Typography variant="h6">Test Warning Triggered!</Typography>
              <Typography>
                In a real application, the warning dialog would appear now. You have less than 30 seconds before automatic logout.
              </Typography>
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Current Status */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TimerIcon color="primary" />
                    Current Status
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Current Time
                    </Typography>
                    <Typography variant="h6">
                      {currentTime.toLocaleTimeString()}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Last Activity
                    </Typography>
                    <Typography variant="h6">
                      {formatTime(inactivityControls.getLastActivity())}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Time Since Last Activity
                    </Typography>
                    <Typography variant="h6">
                      {formatDuration(getTimeSinceLastActivity())}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Time Until Logout
                    </Typography>
                    <Typography variant="h6" color={getTimeUntilLogout() < 30000 ? 'error' : 'primary'}>
                      {formatDuration(getTimeUntilLogout())}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Session Progress
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={getProgressPercentage()}
                      color={getProgressPercentage() < 25 ? 'error' : getProgressPercentage() < 50 ? 'warning' : 'primary'}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {Math.round(getProgressPercentage())}% remaining
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Controls */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <RefreshIcon color="primary" />
                    Test Controls
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleResetActivity}
                      startIcon={<RefreshIcon />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Reset Activity Timer
                    </Button>
                    <Typography variant="caption" color="text.secondary">
                      Simulates user activity and resets the inactivity timer
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      color="secondary"
                      onClick={handleCheckInactivity}
                      startIcon={<TimerIcon />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Check Inactivity Now
                    </Button>
                    <Typography variant="caption" color="text.secondary">
                      Manually triggers the inactivity check
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      color="info"
                      onClick={handleDebugStatus}
                      startIcon={<InfoIcon />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Show Debug Status
                    </Button>
                    <Typography variant="caption" color="text.secondary">
                      Shows detailed debug information
                    </Typography>
                  </Box>

                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      <strong>How it works:</strong>
                      <br />
                      • Mouse movements, clicks, keyboard input, and scrolling reset the timer
                      • Warning appears 30 seconds before logout
                      • Automatic logout occurs after 1 minute of inactivity (30 minutes in production)
                      • User can click "Stay Logged In" to continue the session
                    </Typography>
                  </Alert>
                </CardContent>
              </Card>
            </Grid>

            {/* Information */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <InfoIcon color="primary" />
                    Implementation Details
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Chip label="30 Minutes" color="primary" sx={{ mb: 1 }} />
                        <Typography variant="body2">Production Timeout</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Chip label="5 Minutes" color="warning" sx={{ mb: 1 }} />
                        <Typography variant="body2">Warning Time</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Chip label="60 Seconds" color="error" sx={{ mb: 1 }} />
                        <Typography variant="body2">Auto-close Warning</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Chip label="Global" color="success" sx={{ mb: 1 }} />
                        <Typography variant="body2">App-wide Protection</Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    The inactivity logout feature is automatically enabled across the entire application through the SessionManager component in the root layout.
                    It tracks user interactions and automatically logs out users for security when they're away from their computer.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </AppLayout>
  );
}
