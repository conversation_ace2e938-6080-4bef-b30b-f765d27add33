'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  Card,
  CardContent,
  LinearProgress
} from '@mui/material';
import {
  Timer as TimerIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import AppLayout from '@/layout/AppLayout';
import { performCompleteLogout } from '@/utils/auth';
import { useRouter } from 'next/navigation';

/**
 * Debug page for testing inactivity logout without token dependencies
 */
export default function DebugInactivityPage() {
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [showWarning, setShowWarning] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastLogTimeRef = useRef<number>(0);
  
  const TIMEOUT_MS = 60 * 1000; // 1 minute
  const WARNING_MS = 30 * 1000; // 30 seconds
  const CHECK_INTERVAL = 5 * 1000; // 5 seconds

  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setLogs(prev => [...prev.slice(-9), logEntry]); // Keep last 10 logs
  }, []);

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Update activity
  const updateActivity = useCallback(() => {
    const now = Date.now();
    
    // Throttle activity updates to once per second
    if (now - lastActivity < 1000) {
      return;
    }
    
    setLastActivity(now);
    setShowWarning(false);
    
    // Only log once every 5 seconds to prevent spam
    if (now - lastLogTimeRef.current > 5000) {
      addLog('🔄 User activity detected, resetting inactivity timer');
      lastLogTimeRef.current = now;
    }
  }, [lastActivity, addLog]);

  // Perform logout
  const performInactivityLogout = useCallback(async () => {
    addLog('⏰ User inactive for 1 minute. Logging out...');
    
    try {
      // Clear all timers
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
        checkIntervalRef.current = null;
      }

      // Perform complete logout
      performCompleteLogout();
      
      // Redirect to login page
      router.push('/login?reason=inactivity');
    } catch (error) {
      addLog('❌ Error during inactivity logout: ' + error);
      // Force redirect even if logout fails
      window.location.href = '/login?reason=inactivity';
    }
  }, [router, addLog]);

  // Check inactivity
  const checkInactivity = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivity;
    const timeLeft = TIMEOUT_MS - timeSinceLastActivity;

    addLog(`🔍 Inactivity check: ${Math.round(timeSinceLastActivity / 1000)}s since activity, ${Math.round(timeLeft / 1000)}s left`);

    // Show warning if approaching timeout
    if (timeLeft <= WARNING_MS && timeLeft > 0 && !showWarning) {
      setShowWarning(true);
      const secondsLeft = Math.ceil(timeLeft / 1000);
      addLog(`⚠️ Showing inactivity warning: ${secondsLeft} seconds left`);
    }

    // Logout if timeout exceeded
    if (timeSinceLastActivity >= TIMEOUT_MS) {
      performInactivityLogout();
    }
  }, [lastActivity, showWarning, addLog, performInactivityLogout]);

  // Set up activity listeners and interval
  useEffect(() => {
    addLog('🔧 Setting up debug inactivity tracking');

    const events = ['mousedown', 'keydown', 'scroll', 'touchstart', 'click'];
    
    // Add event listeners for user activity
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // Set up periodic inactivity checking
    checkIntervalRef.current = setInterval(checkInactivity, CHECK_INTERVAL);

    // Initial activity timestamp
    updateActivity();

    return () => {
      addLog('🧹 Cleaning up debug inactivity tracking');
      
      // Remove event listeners
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });

      // Clear intervals
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
        checkIntervalRef.current = null;
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [updateActivity, checkInactivity, addLog]);

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimeUntilLogout = (): number => {
    const timeSinceLastActivity = Date.now() - lastActivity;
    return Math.max(0, TIMEOUT_MS - timeSinceLastActivity);
  };

  const getTimeSinceLastActivity = (): number => {
    return Date.now() - lastActivity;
  };

  const getProgressPercentage = (): number => {
    const timeUntilLogout = getTimeUntilLogout();
    return Math.max(0, (timeUntilLogout / TIMEOUT_MS) * 100);
  };

  const handleResetActivity = () => {
    updateActivity();
    addLog('🔄 Manual activity reset triggered');
  };

  const handleCheckInactivity = () => {
    checkInactivity();
    addLog('🔍 Manual inactivity check triggered');
  };

  return (
    <AppLayout>
      <Container maxWidth="lg">
        <Box sx={{ py: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SecurityIcon color="primary" />
            Debug Inactivity Logout
          </Typography>

          <Typography variant="body1" color="text.secondary" paragraph>
            This is a debug page that tests inactivity logout without token dependencies.
            Timeout: 1 minute, Warning: 30 seconds, Check interval: 5 seconds.
          </Typography>

          {showWarning && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Typography variant="h6">Warning: You will be logged out soon!</Typography>
              <Typography>
                You have been inactive and will be automatically logged out in {Math.ceil(getTimeUntilLogout() / 1000)} seconds.
              </Typography>
            </Alert>
          )}

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TimerIcon color="primary" />
                Current Status
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Current Time: {currentTime.toLocaleTimeString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Last Activity: {formatTime(lastActivity)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Time Since Last Activity: {formatDuration(getTimeSinceLastActivity())}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Time Until Logout: {formatDuration(getTimeUntilLogout())}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Session Progress
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={getProgressPercentage()}
                  color={getProgressPercentage() < 25 ? 'error' : getProgressPercentage() < 50 ? 'warning' : 'primary'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {Math.round(getProgressPercentage())}% remaining
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleResetActivity}
                  startIcon={<RefreshIcon />}
                  size="small"
                >
                  Reset Activity
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={handleCheckInactivity}
                  startIcon={<TimerIcon />}
                  size="small"
                >
                  Check Now
                </Button>
              </Box>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InfoIcon color="primary" />
                Debug Logs
              </Typography>
              
              <Box sx={{ 
                bgcolor: 'grey.100', 
                p: 2, 
                borderRadius: 1, 
                fontFamily: 'monospace', 
                fontSize: '0.875rem',
                maxHeight: 300,
                overflowY: 'auto'
              }}>
                {logs.length === 0 ? (
                  <Typography variant="body2" color="text.secondary">
                    No logs yet...
                  </Typography>
                ) : (
                  logs.map((log, index) => (
                    <div key={index}>{log}</div>
                  ))
                )}
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Container>
    </AppLayout>
  );
}
