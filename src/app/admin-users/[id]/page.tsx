"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import { Container, Box, Paper, CircularProgress, Alert } from "@mui/material";
import AppLayout from "@/layout/AppLayout";
import { PageHeader } from "@/components/common/PageHeader";
import { RoleGuard } from "@/components/auth/RoleGuard";
import { AdminUserForm } from "@/components/admin/AdminUserForm";
import { showToast } from "@/utils/toast";
import { updateAdminUser } from "@/services/adminAPI";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { 
  updateAdminUserRequest, 
  setSelectedAdminUser,
  clearAdminUsersError 
} from "@/store/adminUser/redux";

// Placeholder for fetching a single user (implement real API as needed)
async function fetchAdminUserById(id: string): Promise<any> {
  // TODO: Replace with real API call
  try {
    const res = await fetch(`/api/admin/users/${id}`);
    if (!res.ok) throw new Error("Failed to fetch user");
    return await res.json();
  } catch (e: any) {
    throw e;
  }
}

export default function AdminUserPage() {
  const router = useRouter();
  const params = useParams();
  const dispatch = useAppDispatch();
  const { selectedAdminUser, updateLoading, error: reduxError } = useAppSelector((state) => state.adminUser);
  
  const id = params?.id as string | undefined;
  const isCreateMode = id === "create";

  const [loading, setLoading] = useState(!isCreateMode);
  const [error, setError] = useState<string | null>(null);
  const [adminUser, setAdminUser] = useState<any | undefined>(undefined);

  // Mock current user role - replace with real auth context if available
  const currentUserRole = "super_admin";

  // Fetch user for edit mode
  useEffect(() => {
    if (!isCreateMode && id) {
      // Check if we have the user in Redux state first
      if (selectedAdminUser && selectedAdminUser.uuid === id) {
        setAdminUser(selectedAdminUser);
        setLoading(false);
      } else {
        // Fallback to API fetch
        setLoading(true);
        setError(null);
        fetchAdminUserById(id)
          .then((data) => {
            setAdminUser(data);
            setLoading(false);
          })
          .catch((err: any) => {
            setError(err.message || "Failed to load user");
            setLoading(false);
          });
      }
    }
  }, [id, isCreateMode, selectedAdminUser]);

  // Handle form submit
  const handleSubmit = useCallback(
    async (data: any) => {
      try {
        if (isCreateMode) {
          // AdminUserForm handles create logic internally
          return;
        } else if (id) {
          const loadingToast = showToast.loading("Updating admin user...");
          
          // Use Redux action for update
          dispatch(updateAdminUserRequest({ uuid: id, data }));
          
          // Note: In a real app, you'd wait for the saga to complete
          // For now, we'll show success and redirect after a delay
          setTimeout(() => {
            showToast.dismiss(loadingToast);
            if (!updateLoading) {
              showToast.success("Admin user updated successfully!");
              router.push("/admin-users");
            }
          }, 1000);
        }
      } catch (err: any) {
        showToast.error(err.message || "An error occurred");
      }
    },
    [id, isCreateMode, router, dispatch, updateLoading]
  );

  const handleCancel = useCallback(() => {
    router.push("/admin-users");
  }, [router]);

  return (
    <RoleGuard requiredRole="admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            <PageHeader
              title={isCreateMode ? "Create Admin User" : "Edit Admin User"}
              subtitle={
                isCreateMode
                  ? "Add a new administrator to the system"
                  : "Edit administrator details"
              }
              breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Admin Users", href: "/admin-users" },
                { label: isCreateMode ? "Create User" : "Edit User" },
              ]}
            />
            <Paper elevation={3} sx={{ p: 4, mt: 3 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", py: 8 }}>
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Alert severity="error">{error}</Alert>
              ) : (
                <AdminUserForm
                  adminUser={isCreateMode ? undefined : adminUser}
                  currentUserRole={currentUserRole}
                  onSubmit={handleSubmit}
                  onCancel={handleCancel}
                  loading={updateLoading}
                  error={reduxError}
                />
              )}
            </Paper>
          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
}
