'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { AdminListTable } from '@/components/admin/AdminListTable';
import { AdminUserForm } from '@/components/admin/AdminUserForm';
import { ConfirmDialog } from '@/components/common/ConfirmDialog';
import { RoleGuard } from '@/components/auth/RoleGuard';
import { PageHeader } from '@/components/common/PageHeader';
import {
  fetchAdminList,
  getAdminStats,
  filterAdminUsers,
  sortAdminUsers,
  paginateAdminUsers,
  AdminTableUser
} from '@/services/adminListAPI';
import AppLayout from '@/layout/AppLayout';
import { navigate } from '@/utils/routerService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchAdminUsersListRequest,
  deleteAdminUserRequest,
  setSelectedAdminUser,
  clearAdminUsersError,
} from '@/store/adminUser/redux';
import { showToast } from '@/utils/toast';

export default function AdminUsersPage() {
  const dispatch = useAppDispatch();
  const { 
    adminUsersLoading, 
    adminUsersError, 
    deleteLoading,
    selectedAdminUser 
  } = useAppSelector((state) => state.adminUser);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminUsers, setAdminUsers] = useState<AdminTableUser[]>([]);
  const [allUsers, setAllUsers] = useState<AdminTableUser[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [adminUserToDelete, setAdminUserToDelete] = useState<AdminTableUser | null>(null);

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = 'super_admin';

  // Table state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState<string>('username');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [total, setTotal] = useState(0);

  // Filter state
  const [filters, setFilters] = useState({
    search: '',
    role: 'all',
  });

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    processData();
  }, [allUsers, page, pageSize, sortBy, sortOrder, filters]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const users = await fetchAdminList();
      setAllUsers(users);

      const statsData = await getAdminStats(users);
      setStats(statsData);
    } catch (err: any) {
      console.error('Admin users data fetch error:', err);
      setError(err.message || 'Failed to load admin users data');
    } finally {
      setLoading(false);
    }
  };

  const processData = () => {
    if (allUsers.length === 0) return;

    // Filter users
    const filtered = filterAdminUsers(allUsers, filters.search, filters.role);

    // Sort users
    const sorted = sortAdminUsers(filtered, sortBy, sortOrder);

    // Paginate users
    const paginated = paginateAdminUsers(sorted, page, pageSize);

    setAdminUsers(paginated.users);
    setTotal(paginated.total);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, search: event.target.value }));
    setPage(1); // Reset to first page when searching
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // Reset to first page when filtering
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };


  const handleCreateAdminUser = async () => {
    try {
      // The form will handle the API call
      setShowAddDialog(false);
      // Refresh data after successful creation
      setTimeout(() => {
        fetchData();
      }, 1000);
    } catch (err: any) {
      throw err; // Let the form handle the error
    }
  };

  const confirmDelete = async () => {
    if (!adminUserToDelete) return;

    try {
      const loadingToast = showToast.loading("Deleting admin user...");
      
      // Use Redux action for delete
      dispatch(deleteAdminUserRequest(adminUserToDelete.id));
      
      // Note: In a real app, you'd wait for the saga to complete
      // For now, we'll show success and refresh after a delay
      setTimeout(() => {
        showToast.dismiss(loadingToast);
        if (!deleteLoading) {
          showToast.success("Admin user deleted successfully!");
          setShowDeleteDialog(false);
          setAdminUserToDelete(null);
          fetchData(); // Refresh the data
        }
      }, 1000);
    } catch (err: any) {
      showToast.error('Failed to delete admin user');
    }
  };

  const handleEdit = (user: AdminTableUser) => {
    // Convert AdminTableUser to AdminUser and set as selected
    const adminUser = {
      uuid: user.id,
      username: user.username,
      email: user.email,
      firstname: user.username.split(' ')[0] || null,
      lastname: user.username.split(' ')[1] || null,
      phone: null,
      countrycode: null,
      isactive: true,
      istemppassword: false,
      emailverified: true,
      roles: user.roles[0] || 'admin',
      createdby: user.createdBy.uuid,
      permissions: [],
    };
    
    dispatch(setSelectedAdminUser(adminUser));
    navigate(`/admin-users/${user.id}`);
  };

  const handleDelete = (user: AdminTableUser) => {
    setAdminUserToDelete(user);
    setShowDeleteDialog(true);
  };



  return (
    <RoleGuard requiredRole="super_admin" currentUserRole={currentUserRole}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Page Header */}
            <PageHeader
              title="Admin User Management"
              subtitle="Manage system administrators and their permissions"
              breadcrumbs={[
                { label: 'Dashboard', href: '/dashboard' },
                { label: 'Admin Users' }
              ]}
              actions={
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => navigate('/admin-users/create')}
                  sx={{ minWidth: 150 }}
                >
                  Add Admin User
                </Button>
              }
            />

            {error && (
              <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                {error}
              </Alert>
            )}

            {/* Statistics Cards */}
            {stats && (
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={2}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                        {stats.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Users
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={2}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 600, color: 'info.main' }}>
                        {stats.superAdmins}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Super Admins
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 600, color: 'secondary.main' }}>
                        {stats.newThisMonth}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        New This Month
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}

            {/* Search and Filters */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      placeholder="Search admin users..."
                      value={filters.search}
                      onChange={handleSearchChange}
                      InputProps={{
                        startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={8}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <FormControl size="small" sx={{ minWidth: 120 }}>
                        <InputLabel>Role</InputLabel>
                        <Select
                          value={filters.role}
                          label="Role"
                          onChange={(e) => handleFilterChange('role', e.target.value)}
                        >
                          <MenuItem value="all">All Roles</MenuItem>
                          <MenuItem value="super_admin">Super Admin</MenuItem>
                          <MenuItem value="admin">Admin</MenuItem>
                          <MenuItem value="moderator">Moderator</MenuItem>
                        </Select>
                      </FormControl>
                      <Button
                        variant="outlined"
                        startIcon={<RefreshIcon />}
                        onClick={fetchData}
                        size="small"
                      >
                        Refresh
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Admin Users Table */}
            <AdminListTable
              users={adminUsers}
              loading={loading}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={page}
              pageSize={pageSize}
              total={total}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              onEdit={handleEdit}
              onDelete={handleDelete}
              currentUserRole={currentUserRole}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Admin User"
              message={`Are you sure you want to delete ${adminUserToDelete?.username} (${adminUserToDelete?.email})? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setAdminUserToDelete(null);
              }}
            />

            {/* Add Admin User Dialog */}
            <Dialog
              open={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              maxWidth="md"
              fullWidth
              PaperProps={{
                sx: {
                  minHeight: '80vh',
                  maxHeight: '90vh',
                },
              }}
            >
              <DialogTitle sx={{ pb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AddIcon color="primary" />
                  Add New Admin User
                </Box>
              </DialogTitle>
              <DialogContent sx={{ pb: 0 }}>
                <AdminUserForm
                  currentUserRole={currentUserRole}
                  onSubmit={handleCreateAdminUser}
                  onCancel={() => setShowAddDialog(false)}
                />
              </DialogContent>

            </Dialog>


          </Box>
        </Container>
      </AppLayout>
    </RoleGuard>
  );
} 