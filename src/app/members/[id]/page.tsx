'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  Alert,
  CircularProgress,
  Button,
  Chip,
  Avatar,
  Grid,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import MemberDetailTabs from '@/components/members/MemberDetailTabs';
import { membersService } from '@/services/members';
import { MemberWithRelations } from '@/types/member';
import MemberStatusChip from '@/components/members/MemberStatusChip';
import AppLayout from '@/layout/AppLayout';

export default function MemberDetailPage() {
  const params = useParams();
  const router = useRouter();
  const member_id = params.id as string;
  
  const [loading, set_loading] = useState(true);
  const [error, set_error] = useState<string | null>(null);
  const [member, set_member] = useState<MemberWithRelations | null>(null);
  const [active_tab, set_active_tab] = useState(0);
  useEffect(() => {
    if (member_id) {
      fetch_member();
    }
  }, [member_id]);

  const fetch_member = async () => {
    try {
      set_loading(true);
      set_error(null);
      const member_data = await membersService.getMember(member_id);
      set_member(member_data);
    } catch (err: any) {
      console.error('Member fetch error:', err);
      set_error(err.message || 'Failed to load member data');
    } finally {
      set_loading(false);
    }
  };

  const handle_tab_change = (new_tab: number) => {
    set_active_tab(new_tab);
  };

  const handle_member_update = () => {
    fetch_member();
  };

  const get_initials = (first_name?: string, last_name?: string) => {
    return `${first_name?.charAt(0) || ''}${last_name?.charAt(0) || ''}`.toUpperCase();
  };

  const format_date = (date_string: string) => {
    return new Date(date_string).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
            <CircularProgress size={60} />
          </Box>
        </Container>
      </AppLayout>
    );
  }

  if (error || !member) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            <Alert severity="error" sx={{ mb: 3 }}>
              {error || 'Member not found'}
            </Alert>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => router.push('/members')}
            >
              Back to Members
            </Button>
          </Box>
        </Container>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link
              color="inherit"
              href="/members"
              onClick={(e) => {
                e.preventDefault();
                router.push('/members');
              }}
              sx={{ cursor: 'pointer' }}
            >
              Members
            </Link>            <Typography color="text.primary">
              {member.firstname} {member.lastname}
            </Typography>
          </Breadcrumbs>

          {/* Header */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>                  <Avatar
                    sx={{ width: 80, height: 80, fontSize: '2rem', bgcolor: 'primary.main' }}
                  >
                    {get_initials(member.firstname, member.lastname)}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#1e3a8a' }}>
                      {member.firstname} {member.lastname}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                      {member.professionaltitle || 'No title specified'}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                      <MemberStatusChip
                        type="membershipTier"
                        value={member.membershiptier}
                        size="small"
                      />
                      <MemberStatusChip
                        type="communityStatus"
                        value={member.communitystatus}
                        size="small"
                      />
                      {member.blacklistReport?.isSpam && (
                        <Chip
                          label="Blacklisted"
                          color="error"
                          size="small"
                          icon={<WarningIcon />}
                        />
                      )}
                    </Box>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>                  <Tooltip title="Refresh">
                    <IconButton onClick={fetch_member} disabled={loading}>
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip>
                  <Button
                    variant="outlined"
                    startIcon={<ArrowBackIcon />}
                    onClick={() => router.push('/members')}
                  >
                    Back to List
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                    onClick={() => {
                      // TODO: Implement edit functionality
                      console.log('Edit member:', member.id);
                    }}
                  >
                    Edit Member
                  </Button>
                </Box>
              </Box>

              {/* Quick Info Grid */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>                    <Typography variant="body2" color="text.secondary">
                      <strong>Email:</strong> {member.loginemail}
                      {member.loginemailverified && (
                        <CheckIcon sx={{ ml: 1, fontSize: '1rem', color: 'success.main' }} />
                      )}
                    </Typography>
                    {member.personalbusinessemail && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Personal Email:</strong> {member.personalbusinessemail}
                      </Typography>
                    )}
                    {member.phone && (
                      <Typography variant="body2" color="text.secondary">
                        <strong>Phone:</strong> {member.phone}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary" component="span">
                      <strong>Identity Type:</strong>
                      <Box sx={{ ml: 1, display: 'inline-block' }}>
                        <MemberStatusChip
                          type="identityType"
                          value={member.identitytype || 'unknown'}
                          size="small"
                          showIcon={false}
                        />
                      </Box>
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>                    <Typography variant="body2" color="text.secondary">
                      <strong>Member ID:</strong> {member.id}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Auth0 ID:</strong> {member.auth0Id}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Created:</strong> {format_date(member.datecreated)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Last Updated:</strong> {format_date(member.dateupdated)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Organizations:</strong> {member.organizations?.length || 0}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Tabs */}          <MemberDetailTabs
            member={member}
            activeTab={active_tab}
            onTabChange={handle_tab_change}
            onMemberUpdate={handle_member_update}
          />
        </Box>
      </Container>
    </AppLayout>
  );
} 