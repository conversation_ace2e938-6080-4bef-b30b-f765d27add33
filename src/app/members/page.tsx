"use client";

import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Autocomplete,
  FormControlLabel,
  Checkbox,
  Divider,
} from "@mui/material";
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { MemberTable } from "@/components/members/MemberTable";
import { ConfirmDialog } from "@/components/common/ConfirmDialog";
import AddMemberDialog from "@/components/members/AddMemberDialog";
import EditMemberDialog from "@/components/members/EditMemberDialog";
import { membersService } from "@/services/members";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
import {
  StatsCardSkeleton,
  PageLoading,
  useLoading,
} from "@/components/common";
import {
  Member,
  MemberWithRelations,
  MemberFilters,
  MemberSearchFilters,
  MemberSearchParams,
  MemberStats,
  MemberExportOptions,
} from "@/types/member";
import AppLayout from "@/layout/AppLayout";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchMembersRequest } from "@/store/members/redux";

export default function MembersPage() {
  const { setIsLoading, setLoadingMessage } = useLoading();

  const dispatch = useAppDispatch();
  const { members, loading, error } = useAppSelector((state) => state.members);

  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [memberToEdit, setMemberToEdit] = useState<Member | null>(null);

  // Table state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const [sortBy, setSortBy] = useState<string>("dateCreated");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [total, setTotal] = useState(0);

  // Basic filter state
  const [filters, setFilters] = useState<MemberFilters>({
    search: "",
    status: "all",
    role: "all",
    membershipType: "all",
    industry: "",
  });

  // Advanced filter state
  const [advancedFilters, setAdvancedFilters] = useState<MemberSearchFilters>({
    search: "",
    membershipTier: [],
    communityStatus: [],
    identityType: [],
    verificationStatus: [],
    organizationId: undefined,
    hasBlacklistIssues: undefined,
    dateCreatedFrom: undefined,
    dateCreatedTo: undefined,
  });

  // Separate effect for filters to avoid infinite loops
  React.useEffect(() => {
    // The original fetchData logic is removed, so this effect is no longer needed
    // as filtering is handled by Redux.
  }, [
    filters.search,
    filters.status,
    filters.role,
    filters.membershipType,
    filters.industry,
  ]);

  // Separate effect for advanced filters
  React.useEffect(() => {
    // The original fetchData logic is removed, so this effect is no longer needed
    // as filtering is handled by Redux.
  }, [
    advancedFilters.membershipTier,
    advancedFilters.communityStatus,
    advancedFilters.identityType,
    advancedFilters.verificationStatus,
    advancedFilters.organizationId,
    advancedFilters.hasBlacklistIssues,
    advancedFilters.dateCreatedFrom,
    advancedFilters.dateCreatedTo,
  ]);

  useEffect(() => {
    dispatch(fetchMembersRequest());
  }, [dispatch]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev: MemberFilters) => ({
      ...prev,
      search: event.target.value,
    }));
    setAdvancedFilters((prev: MemberSearchFilters) => ({
      ...prev,
      search: event.target.value,
    }));
    setPage(1);
  };

  const handleFilterChange = (key: keyof MemberFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleAdvancedFilterChange = (
    key: keyof MemberSearchFilters,
    value: any
  ) => {
    setAdvancedFilters((prev) => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleSortChange = (
    newSortBy: string,
    newSortOrder: "asc" | "desc"
  ) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleSelectionChange = (newSelectedIds: number[]) => {
    setSelectedIds(newSelectedIds);
  };

  const handleEdit = (member: Member) => {
    setMemberToEdit(member);
    setShowEditDialog(true);
  };

  const handleDelete = (member: Member) => {
    setMemberToDelete(member);
    setShowDeleteDialog(true);
  };

  const handleView = (member: Member) => {
    // TODO: Implement view functionality
    console.log("View member:", member);
  };

  const handleVerify = async (memberId: number) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Verifying member...");

      await membersService.verifyMember({
        memberId,
        verificationType: "manual",
        data: {},
      });

      await dispatch(fetchMembersRequest());
      // showSuccess('Member verified successfully'); // Removed as per edit hint
    } catch (err: any) {
      const errorMessage = "Failed to verify member";
      // showError(errorMessage); // Removed as per edit hint
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleEmailVerify = async (memberId: number) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Verifying email...");

      await membersService.verifyEmail(memberId);
      await dispatch(fetchMembersRequest());
      // showSuccess('Email verification sent'); // Removed as per edit hint
    } catch (err: any) {
      const errorMessage = "Failed to verify email";
      // showError(errorMessage); // Removed as per edit hint
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleStatusChange = async (memberId: number, status: string) => {
    try {
      setIsLoading(true);
      setLoadingMessage("Updating member status...");

      await membersService.updateMemberStatus(memberId, status);
      await dispatch(fetchMembersRequest());
      // showSuccess('Member status updated successfully'); // Removed as per edit hint
    } catch (err: any) {
      const errorMessage = "Failed to update member status";
      // showError(errorMessage); // Removed as per edit hint
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleBulkDelete = async () => {
    if (selectedIds.length === 0) return;

    // showConfirmation({ // Removed as per edit hint
    //   title: 'Delete Selected Members',
    //   message: `Are you sure you want to delete ${selectedIds.length} selected member(s)? This action cannot be undone.`,
    //   confirmText: 'Delete',
    //   cancelText: 'Cancel',
    //   severity: 'error',
    //   onConfirm: async () => {
    //     try {
    //       setIsLoading(true);
    //       setLoadingMessage('Deleting selected members...');

    //       await membersService.bulkAction({
    //         memberIds: selectedIds.map(id => id.toString()),
    //         action: 'delete',
    //       });

    //       setSelectedIds([]);
    //       await fetchData();
    //       showSuccess(`${selectedIds.length} member(s) deleted successfully`);
    //     } catch (err: any) {
    //       const errorMessage = 'Failed to delete selected members';
    //       showError(errorMessage);
    //     } finally {
    //       setIsLoading(false);
    //       setLoadingMessage('');
    //     }
    //   },
    // });
  };

  const handleBulkExport = async () => {
    if (selectedIds.length === 0) return;

    try {
      setIsLoading(true);
      setLoadingMessage("Exporting members...");

      const exportOptions: MemberExportOptions = {
        format: "xlsx",
        memberIds: selectedIds.map((id) => id.toString()),
      };
      const result = await membersService.exportMembers(exportOptions);

      // Create download link
      const link = document.createElement("a");
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setSelectedIds([]);
      // showSuccess('Members exported successfully'); // Removed as per edit hint
    } catch (err: any) {
      const errorMessage = "Failed to export selected members";
      // showError(errorMessage); // Removed as per edit hint
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleBulkVerify = async () => {
    if (selectedIds.length === 0) return;

    try {
      setIsLoading(true);
      setLoadingMessage("Verifying selected members...");

      await membersService.bulkAction({
        memberIds: selectedIds.map((id) => id.toString()),
        action: "verify",
      });

      setSelectedIds([]);
      await dispatch(fetchMembersRequest());
      // showSuccess(`${selectedIds.length} member(s) verified successfully`); // Removed as per edit hint
    } catch (err: any) {
      const errorMessage = "Failed to verify selected members";
      // showError(errorMessage); // Removed as per edit hint
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const handleAddMember = () => {
    setShowAddDialog(true);
  };

  const confirmDelete = async () => {
    if (!memberToDelete) return;

    try {
      setIsLoading(true);
      setLoadingMessage("Deleting member...");

      await membersService.deleteMember(memberToDelete.id.toString());
      setShowDeleteDialog(false);
      setMemberToDelete(null);
      await dispatch(fetchMembersRequest());
      // showSuccess('Member deleted successfully'); // Removed as per edit hint
    } catch (err: any) {
      const errorMessage = "Failed to delete member";
      // showError(errorMessage); // Removed as per edit hint
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  };

  const clearFilters = () => {
    setFilters({
      search: "",
      status: "all",
      role: "all",
      membershipType: "all",
      industry: "",
    });
    setAdvancedFilters({
      search: "",
      membershipTier: [],
      communityStatus: [],
      identityType: [],
      verificationStatus: [],
      organizationId: undefined,
      hasBlacklistIssues: undefined,
      dateCreatedFrom: undefined,
      dateCreatedTo: undefined,
    });
    setPage(1);
    // showInfo('Filters cleared'); // Removed as per edit hint
  };

  // Show loading state
  if (loading && !members) {
    return (
      <AppLayout>
        <Container maxWidth="xl">
          <PageLoading message="Loading members..." />
        </Container>
      </AppLayout>
    );
  }

  return (
    <ErrorBoundary isPageLevel={true}>
      <AppLayout>
        <Container maxWidth="xl">
          <Box sx={{ py: 3 }}>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                alignItems: { xs: "flex-start", md: "center" },
                justifyContent: "space-between",
                mb: 4,
                gap: { xs: 2, md: 0 },
              }}
            >
              <Box>
                <Typography
                  variant="h3"
                  component="h1"
                  gutterBottom
                  sx={{
                    color: "#1e3a8a",
                    fontWeight: 700,
                    fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.5rem" },
                  }}
                >
                  Members Management
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: "0.875rem", md: "1rem" },
                    display: { xs: "none", sm: "block" },
                  }}
                >
                  Manage and organize your chamber members with advanced
                  filtering and bulk operations
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  flexDirection: { xs: "column", sm: "row" },
                  width: { xs: "100%", sm: "auto" },
                }}
              >
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => dispatch(fetchMembersRequest())}
                  disabled={loading}
                  fullWidth={false}
                  sx={{ minWidth: { xs: "100%", sm: "auto" } }}
                >
                  <Box sx={{ display: { xs: "none", sm: "block" } }}>
                    Refresh
                  </Box>
                  <Box sx={{ display: { xs: "block", sm: "none" } }}>
                    Refresh
                  </Box>
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddMember}
                  fullWidth={false}
                  sx={{ minWidth: { xs: "100%", sm: "auto" } }}
                >
                  <Box sx={{ display: { xs: "none", sm: "block" } }}>
                    Add Member
                  </Box>
                  <Box sx={{ display: { xs: "block", sm: "none" } }}>Add</Box>
                </Button>
              </Box>
            </Box>

            {/* Stats cards removed due to missing 'status' property on Member */}

            {/* Search and Filters */}
            <Card sx={{ mb: 3 }}>
              <CardContent sx={{ p: { xs: 2, md: 3 } }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      placeholder="Search members by name, email, phone, or title..."
                      value={filters.search}
                      onChange={handleSearchChange}
                      size="small"
                      InputProps={{
                        startAdornment: (
                          <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
                        ),
                      }}
                    />
                  </Grid>
                  {/* <Grid item xs={12} md={8}>
                    <Box
                      sx={{
                        display: "flex",
                        gap: { xs: 1, md: 2 },
                        alignItems: "center",
                        flexWrap: "wrap",
                      }}
                    >
                      <FormControl
                        size="small"
                        sx={{ minWidth: { xs: "100%", sm: 120 } }}
                      >
                        <InputLabel>Status</InputLabel>
                        <Select
                          value={filters.status}
                          label="Status"
                          onChange={(e) =>
                            handleFilterChange("status", e.target.value)
                          }
                        >
                          <MenuItem value="all">All Status</MenuItem>
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                          <MenuItem value="pending">Pending</MenuItem>
                        </Select>
                      </FormControl>
                      <FormControl
                        size="small"
                        sx={{ minWidth: { xs: "100%", sm: 120 } }}
                      >
                        <InputLabel>Role</InputLabel>
                        <Select
                          value={filters.role}
                          label="Role"
                          onChange={(e) =>
                            handleFilterChange("role", e.target.value)
                          }
                        >
                          <MenuItem value="all">All Roles</MenuItem>
                          <MenuItem value="admin">Admin</MenuItem>
                          <MenuItem value="member">Member</MenuItem>
                          <MenuItem value="premium">Premium</MenuItem>
                        </Select>
                      </FormControl>
                      <FormControl
                        size="small"
                        sx={{ minWidth: { xs: "100%", sm: 140 } }}
                      >
                        <InputLabel>Membership</InputLabel>
                        <Select
                          value={filters.membershipType}
                          label="Membership"
                          onChange={(e) =>
                            handleFilterChange("membershipType", e.target.value)
                          }
                        >
                          <MenuItem value="all">All Types</MenuItem>
                          <MenuItem value="basic">Basic</MenuItem>
                          <MenuItem value="premium">Premium</MenuItem>
                          <MenuItem value="enterprise">Enterprise</MenuItem>
                        </Select>
                      </FormControl>
                      <FormControl
                        size="small"
                        sx={{ minWidth: { xs: "100%", sm: 140 } }}
                      >
                        <InputLabel>Industry</InputLabel>
                        <Select
                          value={filters.industry}
                          label="Industry"
                          onChange={(e) =>
                            handleFilterChange("industry", e.target.value)
                          }
                        >
                          <MenuItem value="">All Industries</MenuItem>
                    
                        </Select>
                      </FormControl>
                      <Box
                        sx={{
                          display: "flex",
                          gap: 1,
                          width: { xs: "100%", sm: "auto" },
                          justifyContent: {
                            xs: "space-between",
                            sm: "flex-start",
                          },
                        }}
                      >
                        <Button
                          variant="outlined"
                          startIcon={<FilterIcon />}
                          onClick={() =>
                            setShowAdvancedFilters(!showAdvancedFilters)
                          }
                          size="small"
                        >
                          <Box sx={{ display: { xs: "none", sm: "block" } }}>
                            Advanced
                          </Box>
                          <Box sx={{ display: { xs: "block", sm: "none" } }}>
                            Filters
                          </Box>
                        </Button>
                        <Button
                          variant="text"
                          onClick={clearFilters}
                          size="small"
                        >
                          Clear
                        </Button>
                      </Box>
                    </Box>
                  </Grid> */}
                </Grid>

                {/* Advanced Filters */}
                {showAdvancedFilters && (
                  <Box sx={{ mt: 3 }}>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Autocomplete
                          multiple
                          options={["basic", "premium", "enterprise", "vip"]}
                          value={advancedFilters.membershipTier || []}
                          onChange={(_, value) =>
                            handleAdvancedFilterChange("membershipTier", value)
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Membership Tiers"
                              size="small"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Autocomplete
                          multiple
                          options={[
                            "unverified",
                            "verified",
                            "pending",
                            "rejected",
                          ]}
                          value={advancedFilters.communityStatus || []}
                          onChange={(_, value) =>
                            handleAdvancedFilterChange("communityStatus", value)
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Community Status"
                              size="small"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Autocomplete
                          multiple
                          options={["business", "individual", "organization"]}
                          value={advancedFilters.identityType || []}
                          onChange={(_, value) =>
                            handleAdvancedFilterChange("identityType", value)
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Identity Type"
                              size="small"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Autocomplete
                          options={[]}
                          getOptionLabel={(_option) => ""}
                          value={null}
                          onChange={(_, _value) =>
                            handleAdvancedFilterChange(
                              "organizationId",
                              undefined
                            )
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Organization"
                              size="small"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={
                                advancedFilters.hasBlacklistIssues === true
                              }
                              onChange={(e) =>
                                handleAdvancedFilterChange(
                                  "hasBlacklistIssues",
                                  e.target.checked ? true : undefined
                                )
                              }
                            />
                          }
                          label="Has Blacklist Issues"
                          sx={{
                            "& .MuiFormControlLabel-label": {
                              fontSize: { xs: "0.875rem", md: "1rem" },
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <DatePicker
                          label="Created From"
                          value={
                            advancedFilters.dateCreatedFrom
                              ? new Date(advancedFilters.dateCreatedFrom)
                              : null
                          }
                          onChange={(date) =>
                            handleAdvancedFilterChange(
                              "dateCreatedFrom",
                              date?.toISOString()
                            )
                          }
                          slotProps={{
                            textField: {
                              size: "small",
                              sx: { fontSize: { xs: "0.875rem", md: "1rem" } },
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <DatePicker
                          label="Created To"
                          value={
                            advancedFilters.dateCreatedTo
                              ? new Date(advancedFilters.dateCreatedTo)
                              : null
                          }
                          onChange={(date) =>
                            handleAdvancedFilterChange(
                              "dateCreatedTo",
                              date?.toISOString()
                            )
                          }
                          slotProps={{
                            textField: {
                              size: "small",
                              sx: { fontSize: { xs: "0.875rem", md: "1rem" } },
                            },
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </CardContent>
            </Card>

            {/* Members Table */}
            <MemberTable
              members={members as any}
              loading={loading}
              selectedIds={selectedIds}
              onSelectionChange={handleSelectionChange}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onView={handleView}
              onVerify={handleVerify}
              onEmailVerify={handleEmailVerify}
              onStatusChange={handleStatusChange}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              page={page}
              pageSize={pageSize}
              total={total}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              onBulkDelete={handleBulkDelete}
              onBulkExport={handleBulkExport}
              onBulkVerify={handleBulkVerify}
            />

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
              open={showDeleteDialog}
              title="Delete Member"
              message={`Are you sure you want to delete ${memberToDelete?.firstname} ${memberToDelete?.lastname}? This action cannot be undone.`}
              onConfirm={confirmDelete}
              onClose={() => {
                setShowDeleteDialog(false);
                setMemberToDelete(null);
              }}
            />

            {/* Add Member Dialog */}
            <AddMemberDialog
              open={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              onSuccess={() => {
                setShowAddDialog(false);
                dispatch(fetchMembersRequest());
              }}
            />

            {/* Edit Member Dialog */}
            {memberToEdit && (
              <EditMemberDialog
                open={showEditDialog}
                member={memberToEdit}
                onClose={() => {
                  setShowEditDialog(false);
                  setMemberToEdit(null);
                }}
                onSuccess={() => {
                  setShowEditDialog(false);
                  setMemberToEdit(null);
                  dispatch(fetchMembersRequest());
                }}
              />
            )}
          </Box>
        </Container>
      </AppLayout>
    </ErrorBoundary>
  );
}
