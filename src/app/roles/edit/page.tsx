"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { RoleForm } from "@/components/roles/RoleForm";
import { Box, CircularProgress, Typography } from "@mui/material";
import { useRoles } from "@/context/RolesContext";
import { showToast } from "@/utils/toast";

export default function EditRolePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const slug = searchParams.get("slug");
  const { roles, loading, error, fetchRoles, updateRole } = useRoles();
  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const role = roles.find((r) => r.slug === slug);

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 8 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!role) {
    return (
      <Box sx={{ p: 4 }}>
        <Typography color="error">Role not found.</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 900, mx: "auto", mt: 4, mb: 4 }}>
      <RoleForm
        role={role}
        onSubmit={async (data) => {
          setSubmitting(true);
          setFormError(null);
          try {
            await updateRole(role, data);
            showToast.success("Role updated successfully!");
            setTimeout(() => {
              router.push("/roles");
            }, 1200);
          } catch (err: any) {
            setFormError(err.message || "Failed to update role");
            showToast.error(err.message || "Failed to update role");
          } finally {
            setSubmitting(false);
          }
        }}
        onCancel={() => router.push("/roles")}
        error={formError}
        loading={submitting}
        currentUserRole="admin"
      />
    </Box>
  );
}
