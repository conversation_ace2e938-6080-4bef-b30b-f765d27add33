"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  <PERSON>readc<PERSON><PERSON>,
  Link,
} from "@mui/material";
import {
  Add as AddIcon,
  Search as SearchIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
} from "@mui/icons-material";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { RoleTable } from "@/components/roles/RoleTable";
import { RoleForm } from "@/components/roles/RoleForm";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import { PageHeader } from "@/components/ui/PageHeader";
import { RoleGuard } from "@/components/ui/RoleGuard";
import {
  fetchRolesRequest,
  upsertRoleRequest,
  deleteRoleRequest,
  bulkDeleteRolesRequest,
} from "@/store/roles/redux";
import { Role as RoleType } from "@/types/role";
import AppLayout from "@/layout/AppLayout";
import { useCurrentUserRole } from "@/store/adminUser/selector";

export default function RolesPage() {
  const dispatch = useAppDispatch();
  const { roles, loading, error } = useAppSelector((state) => state.roles);
  const currentUserRole = useCurrentUserRole();
  // State
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleType | null>(null);
  const [deletingRole, setDeletingRole] = useState<RoleType | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchRolesRequest());
  }, [dispatch]);
  // Filter roles based on search query
  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleEditRole = (role: RoleType) => {
    setEditingRole(role);
    setShowEditDialog(true);
    setFormError(null);
  };

  // Handle role delete
  const handleDeleteRole = (role: RoleType) => {
    setDeletingRole(role);
    setShowDeleteDialog(true);
  };

  // Handle assign permissions (placeholder for future implementation)
  const handleAssignPermissions = (role: RoleType) => {
    // This would open a permissions management dialog
    console.log("Assign permissions for role:", role.name);
  };
  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      console.log("🎯 Roles Page: Form submission data:", data);
      console.log("🎯 Roles Page: Editing role:", editingRole);

      if (editingRole) {
        // For update, we need to identify the role by name
        const updateData = {
          ...data,
          role: {
            ...data.role,
            name: editingRole.name, // Use name for identification in update
          },
        };
        console.log("🎯 Roles Page: Update data:", updateData);
        dispatch(upsertRoleRequest(updateData));
      } else {
        console.log("🎯 Roles Page: Create data:", data);
        dispatch(upsertRoleRequest(data));
      }
      setShowAddDialog(false);
      setShowEditDialog(false);
      setEditingRole(null);
      setFormError(null);
    } catch (err: any) {
      setFormError(err.message || "Failed to save role");
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedRoles.length > 0) {
      dispatch(bulkDeleteRolesRequest(selectedRoles));
      setSelectedRoles([]);
    }
  };

  // Get role statistics
  const getRoleStats = () => {
    const totalRoles = roles.length;
    const systemRoles = 3;
    const customRoles = totalRoles - systemRoles;
    const totalPermissions = 44;

    return { totalRoles, systemRoles, customRoles, totalPermissions };
  };

  const stats = getRoleStats();

  return (
    <AppLayout>
      <Box sx={{ p: 3 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 3 }}>
          <Link href="/dashboard" color="inherit" underline="hover">
            Dashboard
          </Link>
          <Typography color="text.primary">Roles</Typography>
        </Breadcrumbs>

        {/* Page Header */}
        <PageHeader
          title="Role Management"
          subtitle="Manage user roles and permissions"
          icon={<SecurityIcon />}
          actions={
            <Box sx={{ display: "flex", gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  dispatch(fetchRolesRequest());
                }}
                disabled={loading}
              >
                Refresh
              </Button>
              <RoleGuard requiredPermissions={["roles.create"]}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    setShowAddDialog(true);
                    setFormError(null);
                  }}
                >
                  Add Role
                </Button>
              </RoleGuard>
            </Box>
          }
        />

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error + " Please try again."}
          </Alert>
        )}

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <SecurityIcon color="primary" />
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.totalRoles}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Roles
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <TrendingUpIcon color="success" />
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.customRoles}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Custom Roles
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <SecurityIcon color="warning" />
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.systemRoles}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      System Roles
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          {/* <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <SecurityIcon color="info" />
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.totalPermissions}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Permissions
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid> */}
        </Grid>

        {/* Search and Actions */}
        <Box sx={{ display: "flex", gap: 2, mb: 3, flexWrap: "wrap" }}>
          <TextField
            placeholder="Search roles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300, flexGrow: 1 }}
          />

          {selectedRoles.length > 0 && (
            <RoleGuard requiredPermissions={["roles.delete"]}>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleBulkDelete}
                disabled={loading}
              >
                Delete Selected ({selectedRoles.length})
              </Button>
            </RoleGuard>
          )}
        </Box>

        {/* Roles Table */}
        <RoleTable
          roles={filteredRoles}
          onEdit={handleEditRole}
          onDelete={handleDeleteRole}
          onAssignPermissions={handleAssignPermissions}
          selectedRoles={selectedRoles}
          onSelectionChange={setSelectedRoles}
          loading={loading}
          currentUserRole="admin" // This should come from auth context
        />

        {/* Add Role Dialog */}
        <Dialog
          open={showAddDialog}
          onClose={() => setShowAddDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Add New Role</DialogTitle>
          <DialogContent>
            <RoleForm
              onSubmit={handleFormSubmit}
              onCancel={() => setShowAddDialog(false)}
              loading={loading}
              error={formError}
              currentUserRole="admin"
            />
          </DialogContent>
        </Dialog>

        {/* Edit Role Dialog */}
        <Dialog
          open={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Edit Role</DialogTitle>
          <DialogContent>
            {editingRole && (
              <RoleForm
                role={editingRole}
                onSubmit={handleFormSubmit}
                onCancel={() => setShowEditDialog(false)}
                loading={loading}
                error={formError}
                currentUserRole={currentUserRole}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          open={showDeleteDialog}
          title="Delete Role"
          message={
            deletingRole
              ? `Are you sure you want to delete the role "${deletingRole.name}"? This action cannot be undone.`
              : ""
          }
          onConfirm={() => {
            if (deletingRole) {
              dispatch(
                deleteRoleRequest(deletingRole.slug || deletingRole.name)
              );
              setShowDeleteDialog(false);
              setDeletingRole(null);
            }
          }}
          onCancel={() => setShowDeleteDialog(false)}
          loading={loading}
          confirmText="Delete"
          cancelText="Cancel"
          severity="error"
        />
      </Box>
    </AppLayout>
  );
}
