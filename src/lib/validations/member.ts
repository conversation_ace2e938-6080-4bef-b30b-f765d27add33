import { z } from 'zod';

// Phone number validation regex (supports various formats)
const phoneRegex = /^(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;

// Email validation
const emailSchema = z.string().email('Please enter a valid email address');

// Phone validation
const phoneSchema = z.string().regex(phoneRegex, 'Please enter a valid phone number').optional().or(z.literal(''));

// Organization schema
export const organizationSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, 'Organization name is required'),
  address1: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal('')),
  annualRevenue: z.string().optional(),
  industry: z.string().optional(),
  yearFounded: z.string().optional(),
  companySize: z.string().optional(),
});

// Feature flag schema
export const featureFlagSchema = z.object({
  id: z.number().optional(),
  memberId: z.number().optional(),
  featureHandle: z.string().min(1, 'Feature handle is required'),
  enabled: z.boolean(),
});

// Verification data schema
export const verificationDataSchema = z.object({
  memberId: z.number().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: emailSchema.optional().or(z.literal('')),
  phone: phoneSchema,
  professionalTitle: z.string().optional(),
  verificationStatus: z.enum(['requires_review', 'in_progress', 'completed', 'rejected']).optional(),
  verificationType: z.enum(['auto', 'manual']).optional(),
});

// Member form schema
export const memberFormSchema = z.object({
  // Basic Information
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  loginEmail: emailSchema,
  loginEmailVerified: z.boolean().default(false),
  phone: phoneSchema,
  personalBusinessEmail: emailSchema.optional().or(z.literal('')),
  professionalTitle: z.string().max(100, 'Professional title must be less than 100 characters').optional(),
  
  // Identity and Membership
  identityType: z.enum(['business', 'individual', 'organization']),
  membershipTier: z.enum(['basic', 'premium', 'enterprise', 'vip']),
  communityStatus: z.enum(['unverified', 'verified', 'pending', 'rejected']),
  hasSeenFirstLoginMessage: z.boolean().default(false),
  
  // Organizations
  organizations: z.array(organizationSchema).default([]),
  
  // Verification Data
  verificationData: verificationDataSchema.optional(),
  
  // Feature Flags
  featureFlags: z.array(featureFlagSchema).default([]),
  
  // Auth0 Integration
  createAuth0User: z.boolean().default(false),
  auth0Password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  
  // Metadata
  notes: z.string().optional(),
});

// Create member schema (for new members)
export const createMemberSchema = memberFormSchema.extend({
  createAuth0User: z.boolean().default(false),
  auth0Password: z.string().min(8, 'Password must be at least 8 characters').optional(),
}).refine((data) => {
  if (data.createAuth0User && !data.auth0Password) {
    return false;
  }
  return true;
}, {
  message: 'Password is required when creating Auth0 user',
  path: ['auth0Password'],
});

// Update member schema (for existing members)
export const updateMemberSchema = memberFormSchema.extend({
  id: z.number(),
  auth0Id: z.string().optional(),
});

// Organization creation schema
export const newOrganizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required').max(100, 'Organization name must be less than 100 characters'),
  address1: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().regex(/^\d{5}(-\d{4})?$/, 'Please enter a valid ZIP code').optional().or(z.literal('')),
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal('')),
  annualRevenue: z.string().optional(),
  industry: z.string().optional(),
  yearFounded: z.string().regex(/^\d{4}$/, 'Please enter a valid 4-digit year').optional().or(z.literal('')),
  companySize: z.enum(['1-10', '11-50', '51-100', '101-500', '500+']).optional(),
});

// Bulk member import schema
export const bulkMemberSchema = z.object({
  members: z.array(memberFormSchema.omit({ 
    organizations: true, 
    verificationData: true, 
    featureFlags: true,
    createAuth0User: true,
    auth0Password: true,
  })),
  createAuth0Users: z.boolean().default(false),
  defaultPassword: z.string().min(8, 'Default password must be at least 8 characters').optional(),
});

// Member search filters schema
export const memberSearchFiltersSchema = z.object({
  search: z.string().optional(),
  membershipTier: z.array(z.string()).optional(),
  communityStatus: z.array(z.string()).optional(),
  identityType: z.array(z.string()).optional(),
  verificationStatus: z.array(z.string()).optional(),
  organizationId: z.number().optional(),
  hasBlacklistIssues: z.boolean().optional(),
  dateCreatedFrom: z.string().optional(),
  dateCreatedTo: z.string().optional(),
});

// Export types
export type MemberFormData = z.infer<typeof memberFormSchema>;
export type CreateMemberData = z.infer<typeof createMemberSchema>;
export type UpdateMemberData = z.infer<typeof updateMemberSchema>;
export type OrganizationData = z.infer<typeof organizationSchema>;
export type NewOrganizationData = z.infer<typeof newOrganizationSchema>;
export type FeatureFlagData = z.infer<typeof featureFlagSchema>;
export type VerificationData = z.infer<typeof verificationDataSchema>;
export type MemberSearchFilters = z.infer<typeof memberSearchFiltersSchema>;

// Validation helpers
export const validateEmail = (email: string) => {
  return emailSchema.safeParse(email);
};

export const validatePhone = (phone: string) => {
  return phoneSchema.safeParse(phone);
};

export const validateMemberForm = (data: unknown) => {
  return memberFormSchema.safeParse(data);
};

export const validateCreateMember = (data: unknown) => {
  return createMemberSchema.safeParse(data);
};

export const validateUpdateMember = (data: unknown) => {
  return updateMemberSchema.safeParse(data);
};

export const validateOrganization = (data: unknown) => {
  return organizationSchema.safeParse(data);
};

export const validateNewOrganization = (data: unknown) => {
  return newOrganizationSchema.safeParse(data);
};

// Custom validation functions
export const validateCrossField = (data: MemberFormData) => {
  const errors: Record<string, string> = {};

  // Check if personal email is different from login email
  if (data.personalBusinessEmail && data.personalBusinessEmail === data.loginEmail) {
    errors.personalBusinessEmail = 'Personal email should be different from login email';
  }

  // Check if organization email matches member email
  if (data.organizations.length > 0) {
    const orgWithEmail = data.organizations.find(org => org.email);
    if (orgWithEmail?.email === data.loginEmail) {
      errors.loginEmail = 'Login email should be different from organization email';
    }
  }

  // Validate Auth0 password when creating user
  if (data.createAuth0User && !data.auth0Password) {
    errors.auth0Password = 'Password is required when creating Auth0 user';
  }

  return errors;
};

// Form field validation schemas
export const fieldSchemas = {
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  loginEmail: emailSchema,
  phone: phoneSchema,
  personalBusinessEmail: emailSchema.optional().or(z.literal('')),
  professionalTitle: z.string().max(100, 'Professional title must be less than 100 characters').optional(),
  organizationName: z.string().min(1, 'Organization name is required').max(100, 'Organization name must be less than 100 characters'),
  organizationEmail: emailSchema.optional().or(z.literal('')),
  organizationPhone: phoneSchema,
  zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'Please enter a valid ZIP code').optional().or(z.literal('')),
  yearFounded: z.string().regex(/^\d{4}$/, 'Please enter a valid 4-digit year').optional().or(z.literal('')),
}; 