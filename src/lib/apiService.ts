import { AxiosResponse, AxiosError } from 'axios';
import axios from 'axios';
import apiClient from './api';
import globalErrorHandler from './errorHandler';
import {
  extractData,
  extractMessage,
  measureApiCall,
  generateCacheKey,
  isCacheValid,
  buildPaginationQuery,
  buildFilterQuery,
} from '@/utils/api';
import {
  ApiResponse,
  PaginationParams,
  BaseFilters,
  RetryConfig,
  CacheConfig,
} from '@/types/api';

// Cache storage
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// Mock data for fallback
const mockMembers = [
  {
    id: 1,
    auth0Id: 'auth0|123456789',
    firstName: 'John',
    lastName: 'Smith',
    loginEmail: '<EMAIL>',
    loginEmailVerified: true,
    identityType: 'business',
    personalBusinessEmail: '<EMAIL>',
    phone: '******-0123',
    professionalTitle: 'CEO',
    membershipTier: 'enterprise',
    communityStatus: 'verified',
    hasSeenFirstLoginMessage: true,
    dateCreated: '2024-01-15T10:30:00Z',
    dateUpdated: '2024-01-15T10:30:00Z',
    lastLoginDate: '2024-01-20T14:22:00Z',
    organizations: [
      {
        id: 1,
        name: 'TechCorp Solutions',
        address1: '123 Tech Street',
        city: 'Denver',
        state: 'CO',
        zip: '80202',
        phone: '******-0123',
        email: '<EMAIL>',
        annualRevenue: '$10M+',
        industry: 'Technology',
        yearFounded: '2010',
        companySize: '100-500',
        dateCreated: '2024-01-15T10:30:00Z',
        dateUpdated: '2024-01-15T10:30:00Z',
      }
    ],
    awards: [
      {
        memberId: 1,
        organizationId: 1,
        awardListingElementId: 101,
        status: 'submitted',
        progress: 100,
        isDisqualified: false,
        isPreviousWinner: true,
        isQualified: true,
        isJudged: false,
        isPaid: true,
        isWinner: false,
        dateCreated: '2024-01-15T10:30:00Z',
        dateUpdated: '2024-01-15T10:30:00Z',
      }
    ],
    featureFlags: [
      {
        id: 1,
        memberId: 1,
        featureHandle: 'premium_features',
        enabled: true,
      },
      {
        id: 2,
        memberId: 1,
        featureHandle: 'beta_access',
        enabled: false,
      }
    ],
    verifiedData: {
      memberId: 1,
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      professionalTitle: 'CEO',
      verificationStatus: 'completed',
      verificationType: 'manual',
      dateCreated: '2024-01-15T10:30:00Z',
      dateUpdated: '2024-01-15T10:30:00Z',
    },
    blacklistReport: {
      memberId: 1,
      disposableEmail: false,
      appears: true,
      frequency: 1,
      submitted: '2024-01-15T10:30:00Z',
      updated: '2024-01-15T10:30:00Z',
      spamRate: 0,
      exists: true,
      inAntispamUpdated: '2024-01-15T10:30:00Z',
      dateCreated: '2024-01-15T10:30:00Z',
      dateUpdated: '2024-01-15T10:30:00Z',
      isSpam: false,
    },
  },
  {
    id: 2,
    auth0Id: 'auth0|123456790',
    firstName: 'Sarah',
    lastName: 'Johnson',
    loginEmail: '<EMAIL>',
    loginEmailVerified: true,
    identityType: 'business',
    phone: '******-0124',
    professionalTitle: 'CTO',
    membershipTier: 'premium',
    communityStatus: 'verified',
    hasSeenFirstLoginMessage: true,
    dateCreated: '2024-01-10T09:15:00Z',
    dateUpdated: '2024-01-18T16:45:00Z',
    lastLoginDate: '2024-01-19T11:30:00Z',
    organizations: [
      {
        id: 2,
        name: 'Healthcare Plus',
        address1: '456 Health Ave',
        city: 'Boston',
        state: 'MA',
        zip: '02101',
        phone: '******-0124',
        email: '<EMAIL>',
        annualRevenue: '$5M+',
        industry: 'Healthcare',
        yearFounded: '2015',
        companySize: '50-100',
        dateCreated: '2024-01-10T09:15:00Z',
        dateUpdated: '2024-01-10T09:15:00Z',
      }
    ],
    awards: [],
    featureFlags: [
      {
        id: 3,
        memberId: 2,
        featureHandle: 'premium_features',
        enabled: true,
      }
    ],
    verifiedData: {
      memberId: 2,
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '******-0124',
      professionalTitle: 'CTO',
      verificationStatus: 'completed',
      verificationType: 'manual',
      dateCreated: '2024-01-10T09:15:00Z',
      dateUpdated: '2024-01-10T09:15:00Z',
    },
    blacklistReport: {
      memberId: 2,
      disposableEmail: false,
      appears: true,
      frequency: 1,
      submitted: '2024-01-10T09:15:00Z',
      updated: '2024-01-10T09:15:00Z',
      spamRate: 0,
      exists: true,
      inAntispamUpdated: '2024-01-10T09:15:00Z',
      dateCreated: '2024-01-10T09:15:00Z',
      dateUpdated: '2024-01-10T09:15:00Z',
      isSpam: false,
    },
  },
  {
    id: 3,
    auth0Id: 'auth0|123456791',
    firstName: 'Michael',
    lastName: 'Brown',
    loginEmail: '<EMAIL>',
    loginEmailVerified: false,
    identityType: 'business',
    phone: '******-0125',
    professionalTitle: 'CFO',
    membershipTier: 'basic',
    communityStatus: 'inactive',
    hasSeenFirstLoginMessage: false,
    dateCreated: '2023-12-20T14:20:00Z',
    dateUpdated: '2024-01-05T10:15:00Z',
    lastLoginDate: '2024-01-02T09:45:00Z',
    organizations: [
      {
        id: 3,
        name: 'Finance Solutions',
        address1: '789 Finance Blvd',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        phone: '******-0125',
        email: '<EMAIL>',
        annualRevenue: '$20M+',
        industry: 'Finance',
        yearFounded: '2008',
        companySize: '500+',
        dateCreated: '2023-12-20T14:20:00Z',
        dateUpdated: '2023-12-20T14:20:00Z',
      }
    ],
    awards: [],
    featureFlags: [],
    verifiedData: {
      memberId: 3,
      firstName: 'Michael',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '******-0125',
      professionalTitle: 'CFO',
      verificationStatus: 'requires_review',
      verificationType: 'auto',
      dateCreated: '2023-12-20T14:20:00Z',
      dateUpdated: '2023-12-20T14:20:00Z',
    },
    blacklistReport: {
      memberId: 3,
      disposableEmail: false,
      appears: true,
      frequency: 1,
      submitted: '2023-12-20T14:20:00Z',
      updated: '2023-12-20T14:20:00Z',
      spamRate: 0,
      exists: true,
      inAntispamUpdated: '2023-12-20T14:20:00Z',
      dateCreated: '2023-12-20T14:20:00Z',
      dateUpdated: '2023-12-20T14:20:00Z',
      isSpam: false,
    },
  },
  {
    id: 4,
    auth0Id: 'auth0|*********',
    firstName: 'Emily',
    lastName: 'Davis',
    loginEmail: '<EMAIL>',
    loginEmailVerified: false,
    identityType: 'individual',
    phone: '******-0126',
    professionalTitle: 'Director',
    membershipTier: 'basic',
    communityStatus: 'pending',
    hasSeenFirstLoginMessage: false,
    dateCreated: '2024-01-22T08:30:00Z',
    dateUpdated: '2024-01-22T08:30:00Z',
    organizations: [],
    awards: [],
    featureFlags: [],
    verifiedData: {
      memberId: 4,
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '******-0126',
      professionalTitle: 'Director',
      verificationStatus: 'in_progress',
      verificationType: 'auto',
      dateCreated: '2024-01-22T08:30:00Z',
      dateUpdated: '2024-01-22T08:30:00Z',
    },
    blacklistReport: {
      memberId: 4,
      disposableEmail: false,
      appears: true,
      frequency: 1,
      submitted: '2024-01-22T08:30:00Z',
      updated: '2024-01-22T08:30:00Z',
      spamRate: 0,
      exists: true,
      inAntispamUpdated: '2024-01-22T08:30:00Z',
      dateCreated: '2024-01-22T08:30:00Z',
      dateUpdated: '2024-01-22T08:30:00Z',
      isSpam: false,
    },
  },
  {
    id: 5,
    auth0Id: 'auth0|123456793',
    firstName: 'David',
    lastName: 'Wilson',
    loginEmail: '<EMAIL>',
    loginEmailVerified: true,
    identityType: 'business',
    phone: '******-0127',
    professionalTitle: 'Operations Manager',
    membershipTier: 'premium',
    communityStatus: 'verified',
    hasSeenFirstLoginMessage: true,
    dateCreated: '2024-01-08T11:45:00Z',
    dateUpdated: '2024-01-15T13:20:00Z',
    lastLoginDate: '2024-01-20T15:10:00Z',
    organizations: [
      {
        id: 4,
        name: 'Manufacturing Co',
        address1: '321 Factory St',
        city: 'Detroit',
        state: 'MI',
        zip: '48201',
        phone: '******-0127',
        email: '<EMAIL>',
        annualRevenue: '$15M+',
        industry: 'Manufacturing',
        yearFounded: '2005',
        companySize: '200-500',
        dateCreated: '2024-01-08T11:45:00Z',
        dateUpdated: '2024-01-08T11:45:00Z',
      }
    ],
    awards: [
      {
        memberId: 5,
        organizationId: 4,
        awardListingElementId: 102,
        status: 'in_progress',
        progress: 75,
        isDisqualified: false,
        isPreviousWinner: false,
        isQualified: false,
        isJudged: false,
        isPaid: false,
        isWinner: false,
        dateCreated: '2024-01-08T11:45:00Z',
        dateUpdated: '2024-01-08T11:45:00Z',
      }
    ],
    featureFlags: [
      {
        id: 4,
        memberId: 5,
        featureHandle: 'premium_features',
        enabled: true,
      }
    ],
    verifiedData: {
      memberId: 5,
      firstName: 'David',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '******-0127',
      professionalTitle: 'Operations Manager',
      verificationStatus: 'completed',
      verificationType: 'manual',
      dateCreated: '2024-01-08T11:45:00Z',
      dateUpdated: '2024-01-08T11:45:00Z',
    },
    blacklistReport: {
      memberId: 5,
      disposableEmail: false,
      appears: true,
      frequency: 1,
      submitted: '2024-01-08T11:45:00Z',
      updated: '2024-01-08T11:45:00Z',
      spamRate: 0,
      exists: true,
      inAntispamUpdated: '2024-01-08T11:45:00Z',
      dateCreated: '2024-01-08T11:45:00Z',
      dateUpdated: '2024-01-08T11:45:00Z',
      isSpam: false,
    },
  },
];

const mockStats = {
  total: 5,
  active: 3,
  inactive: 1,
  pending: 1,
  newThisMonth: 2,
  growthRate: 40.0,
};

const mockIndustries = ['Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing'];

// API availability check
let isApiAvailable = true;
let apiCheckAttempted = false;
let apiCheckPromise: Promise<boolean> | null = null;

async function checkApiAvailability(): Promise<boolean> {
  // If we already have a check in progress, wait for it
  if (apiCheckPromise) {
    return apiCheckPromise;
  }
  
  // If we already checked, return the result
  if (apiCheckAttempted) return isApiAvailable;
  
  // Start a new check
  apiCheckPromise = performApiCheck();
  return apiCheckPromise;
}

async function performApiCheck(): Promise<boolean> {
  try {
    apiCheckAttempted = true;
    // Use a direct axios call to avoid interceptors for health check
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/health`, { 
      timeout: 2000, // Reduced timeout for faster fallback
      validateStatus: () => true // Don't throw on any status code
    });
    
    // Check if we got a successful response
    if (response.status >= 200 && response.status < 300) {
      isApiAvailable = true;
      console.log('✅ API is available');
      return true;
    } else {
      throw new Error(`Health check failed with status ${response.status}`);
    }
  } catch (error) {
    console.warn('⚠️ API not available, using mock data');
    isApiAvailable = false;
    return false;
  } finally {
    // Clear the promise so future calls can check again if needed
    apiCheckPromise = null;
  }
}

// Mock data helpers
function getMockMembers(params: any) {
  let filteredMembers = [...mockMembers];

  // Apply search filter
  if (params.filters?.search) {
    const searchTerm = params.filters.search.toLowerCase();
    filteredMembers = filteredMembers.filter(member =>
      (member.firstName?.toLowerCase() || '').includes(searchTerm) ||
      (member.lastName?.toLowerCase() || '').includes(searchTerm) ||
      (member.loginEmail?.toLowerCase() || '').includes(searchTerm) ||
      (member.organizations?.[0]?.name?.toLowerCase() || '').includes(searchTerm)
    );
  }

  // Apply status filter
  if (params.filters?.status && params.filters.status !== 'all') {
    filteredMembers = filteredMembers.filter(member => member.communityStatus === params.filters.status);
  }

  // Apply role filter
  if (params.filters?.role && params.filters.role !== 'all') {
    filteredMembers = filteredMembers.filter(member => member.membershipTier === params.filters.role);
  }

  // Apply membership type filter
  if (params.filters?.membershipType && params.filters.membershipType !== 'all') {
    filteredMembers = filteredMembers.filter(member => member.membershipTier === params.filters.membershipType);
  }

  // Apply industry filter
  if (params.filters?.industry) {
    filteredMembers = filteredMembers.filter(member => 
      member.organizations?.some(org => org.industry === params.filters.industry)
    );
  }

  // Apply sorting
  if (params.sortBy) {
    filteredMembers.sort((a, b) => {
      const aValue = a[params.sortBy as keyof typeof a] || '';
      const bValue = b[params.sortBy as keyof typeof b] || '';
      
      if (aValue < bValue) return params.sortOrder === 'desc' ? 1 : -1;
      if (aValue > bValue) return params.sortOrder === 'desc' ? -1 : 1;
      return 0;
    });
  }

  // Apply pagination
  const total = filteredMembers.length;
  const startIndex = (params.page - 1) * params.pageSize;
  const endIndex = startIndex + params.pageSize;
  const paginatedMembers = filteredMembers.slice(startIndex, endIndex);

  return {
    items: paginatedMembers,
    total,
    page: params.page,
    pageSize: params.pageSize,
    totalPages: Math.ceil(total / params.pageSize),
    hasNext: endIndex < total,
    hasPrevious: params.page > 1,
  };
}

class ApiService {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2,
    retryableStatusCodes: [500, 502, 503, 504],
  };

  // Generic GET request
  async get<T>(
    endpoint: string,
    params?: Record<string, any>,
    config?: {
      cache?: CacheConfig;
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<T> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // Return mock data based on endpoint
      if (endpoint === '/members/stats') {
        return mockStats as T;
      }
      if (endpoint === '/members/industries') {
        return mockIndustries as T;
      }
      if (endpoint.startsWith('/members/') && endpoint.includes('/activity')) {
        return [] as T;
      }
      if (endpoint === '/members/search') {
        return getMockMembers({ filters: params, page: 1, pageSize: 10 }).items as T;
      }
      // For other endpoints, return empty array or null
      return [] as T;
    }

    const cacheKey = config?.cache ? generateCacheKey(endpoint, params) : null;
    
    // Check cache first
    if (cacheKey && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      if (isCacheValid(cached.timestamp, cached.ttl)) {
        return cached.data;
      } else {
        this.cache.delete(cacheKey);
      }
    }

    const apiCall = () => apiClient.get<ApiResponse<T>>(endpoint, { params });
    
    try {
      const { data, duration } = await measureApiCall(apiCall, endpoint);
      const result = extractData(data);
      
      // Cache the result
      if (cacheKey && config?.cache) {
        this.cache.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
          ttl: config.cache.ttl,
        });
      }
      
      return result;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      // If this is a network error and we thought the API was available, 
      // fall back to mock data
      if (!axiosError.response && apiAvailable) {
        console.warn(`🔄 API became unavailable, falling back to mock data for ${endpoint}`);
        // Reset API availability and return mock data
        this.resetApiCheck();
        
        if (endpoint === '/members/stats') {
          return mockStats as T;
        }
        if (endpoint === '/members/industries') {
          return mockIndustries as T;
        }
        if (endpoint.startsWith('/members/') && endpoint.includes('/activity')) {
          return [] as T;
        }
        if (endpoint.startsWith('/members/') && !endpoint.includes('/activity') && !endpoint.includes('/verify') && !endpoint.includes('/status')) {
          // Individual member request
          const id = endpoint.split('/').pop();
          const member = mockMembers.find(m => m.id === parseInt(id || '0'));
          return member as T;
        }
        if (endpoint === '/members/search') {
          return getMockMembers({ filters: params, page: 1, pageSize: 10 }).items as T;
        }
        return [] as T;
      }
      
      // Handle with retry logic
      if (config?.retry || this.defaultRetryConfig) {
        const retryConfig = { ...this.defaultRetryConfig, ...config?.retry };
        return this.retryWithBackoff(apiCall, retryConfig, config?.context);
      }
      
      globalErrorHandler.handleApiError(axiosError, config?.context);
      throw axiosError;
    }
  }

  // Generic POST request
  async post<T>(
    endpoint: string,
    data?: any,
    config?: {
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<T> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // For mock mode, simulate success
      if (endpoint === '/members') {
        const newMember = {
          id: (mockMembers.length + 1).toString(),
          ...data,
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        mockMembers.push(newMember);
        return newMember as T;
      }
      if (endpoint === '/members/bulk') {
        return { success: [], failed: [], message: 'Bulk action completed' } as T;
      }
      return {} as T;
    }

    const apiCall = () => apiClient.post<ApiResponse<T>>(endpoint, data);
    
    try {
      const { data: response, duration } = await measureApiCall(apiCall, endpoint);
      const result = extractData(response);
      
      // Show success message
      const message = extractMessage(response);
      if (message) {
        globalErrorHandler.showSuccess(message);
      }
      
      return result;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      if (config?.retry || this.defaultRetryConfig) {
        const retryConfig = { ...this.defaultRetryConfig, ...config?.retry };
        return this.retryWithBackoff(apiCall, retryConfig, config?.context);
      }
      
      globalErrorHandler.handleApiError(axiosError, config?.context);
      throw axiosError;
    }
  }

  // Generic PUT request
  async put<T>(
    endpoint: string,
    data?: any,
    config?: {
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<T> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // For mock mode, simulate success
      if (endpoint.startsWith('/members/')) {
        const id = endpoint.split('/').pop();
        const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id || '0'));
        if (memberIndex !== -1) {
          mockMembers[memberIndex] = { ...mockMembers[memberIndex], ...data, dateUpdated: new Date().toISOString() };
          return mockMembers[memberIndex] as T;
        }
      }
      return {} as T;
    }

    const apiCall = () => apiClient.put<ApiResponse<T>>(endpoint, data);
    
    try {
      const { data: response, duration } = await measureApiCall(apiCall, endpoint);
      const result = extractData(response);
      
      const message = extractMessage(response);
      if (message) {
        globalErrorHandler.showSuccess(message);
      }
      
      return result;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      if (config?.retry || this.defaultRetryConfig) {
        const retryConfig = { ...this.defaultRetryConfig, ...config?.retry };
        return this.retryWithBackoff(apiCall, retryConfig, config?.context);
      }
      
      globalErrorHandler.handleApiError(axiosError, config?.context);
      throw axiosError;
    }
  }

  // Generic PATCH request
  async patch<T>(
    endpoint: string,
    data?: any,
    config?: {
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<T> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // For mock mode, simulate success
      return {} as T;
    }

    const apiCall = () => apiClient.patch<ApiResponse<T>>(endpoint, data);
    
    try {
      const { data: response, duration } = await measureApiCall(apiCall, endpoint);
      const result = extractData(response);
      
      const message = extractMessage(response);
      if (message) {
        globalErrorHandler.showSuccess(message);
      }
      
      return result;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      if (config?.retry || this.defaultRetryConfig) {
        const retryConfig = { ...this.defaultRetryConfig, ...config?.retry };
        return this.retryWithBackoff(apiCall, retryConfig, config?.context);
      }
      
      globalErrorHandler.handleApiError(axiosError, config?.context);
      throw axiosError;
    }
  }

  // Generic DELETE request
  async delete<T>(
    endpoint: string,
    config?: {
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<T> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // For mock mode, simulate success
      if (endpoint.startsWith('/members/')) {
        const id = endpoint.split('/').pop();
        const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id || '0'));
        if (memberIndex !== -1) {
          mockMembers.splice(memberIndex, 1);
        }
      }
      return { deleted: true } as T;
    }

    const apiCall = () => apiClient.delete<ApiResponse<T>>(endpoint);
    
    try {
      const { data: response, duration } = await measureApiCall(apiCall, endpoint);
      const result = extractData(response);
      
      const message = extractMessage(response);
      if (message) {
        globalErrorHandler.showSuccess(message);
      }
      
      return result;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      if (config?.retry || this.defaultRetryConfig) {
        const retryConfig = { ...this.defaultRetryConfig, ...config?.retry };
        return this.retryWithBackoff(apiCall, retryConfig, config?.context);
      }
      
      globalErrorHandler.handleApiError(axiosError, config?.context);
      throw axiosError;
    }
  }

  // Paginated GET request
  async getPaginated<T>(
    endpoint: string,
    pagination: PaginationParams,
    filters?: BaseFilters,
    config?: {
      cache?: CacheConfig;
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<{ items: T[]; total: number; page: number; pageSize: number; totalPages: number }> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // Return mock paginated data
      const mockParams = { ...pagination, filters };
      return getMockMembers(mockParams) as any;
    }

    const params = {
      ...pagination,
      ...filters,
    };
    
    const queryString = buildPaginationQuery(pagination);
    const filterString = filters ? buildFilterQuery(filters) : '';
    const fullEndpoint = `${endpoint}${queryString}${filterString}`;
    
    try {
      return await this.get<{ items: T[]; total: number; page: number; pageSize: number; totalPages: number }>(
        fullEndpoint,
        undefined,
        config
      );
    } catch (error) {
      const axiosError = error as AxiosError;
      
      // If this is a network error and we thought the API was available, 
      // fall back to mock data
      if (!axiosError.response && apiAvailable) {
        console.warn(`🔄 API became unavailable, falling back to mock data for ${endpoint} (paginated)`);
        // Reset API availability and return mock data
        this.resetApiCheck();
        const mockParams = { ...pagination, filters };
        return getMockMembers(mockParams) as any;
      }
      
      throw error;
    }
  }

  // File upload
  async uploadFile(
    endpoint: string,
    file: File,
    config?: {
      retry?: Partial<RetryConfig>;
      context?: string;
    }
  ): Promise<{ url: string; filename: string; size: number; mimeType: string }> {
    // Check API availability first
    const apiAvailable = await checkApiAvailability();
    
    if (!apiAvailable) {
      // For mock mode, simulate success
      return {
        url: `https://example.com/uploads/${file.name}`,
        filename: file.name,
        size: file.size,
        mimeType: file.type,
      };
    }

    const formData = new FormData();
    formData.append('file', file);
    
    const apiCall = () => apiClient.post<ApiResponse<{ url: string; filename: string; size: number; mimeType: string }>>(
      endpoint,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    try {
      const { data: response, duration } = await measureApiCall(apiCall, endpoint);
      const result = extractData(response);
      
      const message = extractMessage(response);
      if (message) {
        globalErrorHandler.showSuccess(message);
      }
      
      return result;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      if (config?.retry || this.defaultRetryConfig) {
        const retryConfig = { ...this.defaultRetryConfig, ...config?.retry };
        return this.retryWithBackoff(apiCall, retryConfig, config?.context);
      }
      
      globalErrorHandler.handleApiError(axiosError, config?.context);
      throw axiosError;
    }
  }

  // Retry with exponential backoff
  private async retryWithBackoff<T>(
    apiCall: () => Promise<AxiosResponse<ApiResponse<T>>>,
    retryConfig: RetryConfig,
    context?: string
  ): Promise<T> {
    return globalErrorHandler.retryWithBackoff(
      async () => {
        const response = await apiCall();
        return extractData(response);
      },
      retryConfig.maxRetries,
      retryConfig.retryDelay
    );
  }

  // Cache management
  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  getCacheSize(): number {
    return this.cache.size;
  }

  // Set default retry configuration
  setDefaultRetryConfig(config: Partial<RetryConfig>): void {
    this.defaultRetryConfig = { ...this.defaultRetryConfig, ...config };
  }

  // Force API availability check
  async forceApiCheck(): Promise<boolean> {
    apiCheckAttempted = false;
    apiCheckPromise = null;
    return checkApiAvailability();
  }

  // Reset API availability check
  resetApiCheck(): void {
    apiCheckAttempted = false;
    apiCheckPromise = null;
  }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService; 