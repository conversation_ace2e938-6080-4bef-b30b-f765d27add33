'use client';

import React, { useState, useCallback } from 'react';
import { useBackgroundTokenRefresh } from '@/hooks/useBackgroundTokenRefresh';
import { useUserInactivityLogout } from '@/hooks/useUserInactivityLogout';
import { InactivityWarningDialog } from './InactivityWarningDialog';
import { performCompleteLogout } from '@/utils/auth';
import { useRouter } from 'next/navigation';

interface SessionManagerProps {
  // Token refresh settings
  checkInterval?: number; // Check every X milliseconds (default: 30 seconds)
  refreshBuffer?: number; // Refresh X minutes before expiry (default: 5 minutes)
  
  // Inactivity settings
  inactivityTimeout?: number; // Auto logout after X minutes of inactivity (default: 30 minutes)
  enableInactivityLogout?: boolean; // Enable/disable inactivity logout (default: true)
  warningTime?: number; // Show warning X minutes before logout (default: 5)
  showWarningDialog?: boolean; // Show warning dialog (default: true)
  
  // Warning dialog settings
  warningAutoCloseTime?: number; // Auto close warning dialog after X seconds (default: 60)
}

/**
 * Comprehensive session management component that handles:
 * - Background token refresh
 * - User inactivity tracking and automatic logout
 * - Warning dialog for impending logout
 * 
 * Add this to your app layout to enable complete session management
 */
export const SessionManager: React.FC<SessionManagerProps> = ({
  // Token refresh defaults
  checkInterval = 30000, // 30 seconds
  refreshBuffer = 5, // 5 minutes
  
  // Inactivity defaults
  inactivityTimeout = 30, // 30 minutes
  enableInactivityLogout = true,
  warningTime = 5, // 5 minutes
  showWarningDialog = true,
  
  // Warning dialog defaults
  warningAutoCloseTime = 60 // 60 seconds
}) => {
  const router = useRouter();
  const [showWarning, setShowWarning] = useState(false);
  const [warningTimeLeft, setWarningTimeLeft] = useState(0);

  // Handle warning display
  const handleInactivityWarning = useCallback((timeLeft: number) => {
    if (showWarningDialog) {
      setWarningTimeLeft(timeLeft);
      setShowWarning(true);
    }
  }, [showWarningDialog]);

  // Handle manual logout
  const handleLogoutNow = useCallback(async () => {
    setShowWarning(false);
    try {
      performCompleteLogout();
      router.push('/login?reason=manual');
    } catch (error) {
      console.error('Error during manual logout:', error);
      window.location.href = '/login?reason=manual';
    }
  }, [router]);

  // Set up user inactivity logout with warning
  const inactivityLogoutControls = useUserInactivityLogout({
    timeout: inactivityTimeout,
    enabled: enableInactivityLogout,
    warningTime,
    onWarning: handleInactivityWarning,
    checkInterval: 10000 // Check every 10 seconds
  });

  // Handle stay logged in
  const handleStayLoggedIn = useCallback(() => {
    setShowWarning(false);
    // The inactivity hook will automatically reset the timer when user activity is detected
    // We can also manually trigger activity reset
    if (inactivityLogoutControls.resetActivity) {
      inactivityLogoutControls.resetActivity();
    }
  }, [inactivityLogoutControls]);

  // Set up background token refresh
  useBackgroundTokenRefresh({
    checkInterval,
    refreshBuffer
  });

  return (
    <>
      {/* Warning Dialog */}
      {showWarningDialog && (
        <InactivityWarningDialog
          open={showWarning}
          timeLeft={warningTimeLeft}
          onStayLoggedIn={handleStayLoggedIn}
          onLogoutNow={handleLogoutNow}
          autoCloseTime={warningAutoCloseTime}
        />
      )}
    </>
  );
};
