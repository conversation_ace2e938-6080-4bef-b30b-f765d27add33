"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Typography,
  Box,
  Tooltip,
  Checkbox,
  TablePagination,
  TableSortLabel,
  useTheme,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
} from "@mui/icons-material";
import { Role } from "@/types/role";
import { useAppSelector } from "@/store/hooks";
import { useRouter } from "next/navigation";

interface RoleTableProps {
  roles: Role[];
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
  onAssignPermissions: (role: Role) => void;
  selectedRoles: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  loading?: boolean;
  currentUserRole?: string;
}

type SortField = "name" | "createdAt";
type SortOrder = "asc" | "desc";

export function RoleTable({
  roles,
  onEdit,
  onDelete,
  onAssignPermissions,
  selectedRoles,
  onSelectionChange,
  loading = false,
  currentUserRole = "admin",
}: RoleTableProps) {
  const theme = useTheme();
  const [sortField, setSortField] = useState<SortField>("name");
  const [sortOrder, setSortOrder] = useState<SortOrder>("asc");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const router = useRouter();

  // Check if current user can manage the target role
  const canManageRole = (targetRole: Role) => {
    // Simplified logic - in a real app, this would check actual permissions
    return currentUserRole === "admin" || currentUserRole === "super_admin";
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    const isAsc = sortField === field && sortOrder === "asc";
    setSortOrder(isAsc ? "desc" : "asc");
    setSortField(field);
  };

  // Sort roles
  const sortedRoles = [...roles].sort((a, b) => {
    let aValue: any = sortField === "createdAt" ? a.datecreated : a[sortField];
    let bValue: any = sortField === "createdAt" ? b.datecreated : b[sortField];
    if (sortField === "createdAt") {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }
    if (aValue < bValue) {
      return sortOrder === "asc" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOrder === "asc" ? 1 : -1;
    }
    return 0;
  });

  // System roles: treat slugs 'admin', 'super_admin', 'moderator' as system roles
  const isSystemRole = (role: any) =>
    ["admin", "super_admin", "moderator"].includes(role.slug);

  // Pagination
  const paginatedRoles = sortedRoles.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Handle selection
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const selectableRoles = paginatedRoles
        .filter((role) => !isSystemRole(role) && canManageRole(role))
        .map((role) => role.slug);
      onSelectionChange(selectableRoles);
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectRole = (roleSlug: string) => {
    const newSelected = selectedRoles.includes(roleSlug)
      ? selectedRoles.filter((id) => id !== roleSlug)
      : [...selectedRoles, roleSlug];
    onSelectionChange(newSelected);
  };

  return (
    <Paper elevation={2} sx={{ overflow: "hidden" }}>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: theme.palette.grey[50] }}>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={
                    selectedRoles.length > 0 &&
                    selectedRoles.length <
                      paginatedRoles.filter(
                        (r) => !isSystemRole(r) && canManageRole(r)
                      ).length
                  }
                  checked={
                    selectedRoles.length > 0 &&
                    selectedRoles.length ===
                      paginatedRoles.filter(
                        (r) => !isSystemRole(r) && canManageRole(r)
                      ).length
                  }
                  onChange={handleSelectAll}
                  disabled={loading}
                />
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === "name"}
                  direction={sortField === "name" ? sortOrder : "asc"}
                  onClick={() => handleSort("name")}
                >
                  Role Name
                </TableSortLabel>
              </TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Permissions</TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === "createdAt"}
                  direction={sortField === "createdAt" ? sortOrder : "asc"}
                  onClick={() => handleSort("createdAt")}
                >
                  Created
                </TableSortLabel>
              </TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedRoles.map((role) => {
              const isSelected = selectedRoles.includes(role.slug);
              const canManage = canManageRole(role);

              return (
                <TableRow
                  key={role.slug}
                  hover
                  selected={isSelected}
                  sx={{
                    "&:hover": {
                      backgroundColor: theme.palette.action.hover,
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleSelectRole(role.slug)}
                      disabled={loading || isSystemRole(role) || !canManage}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {role.name}
                      </Typography>
                      {isSystemRole(role) && (
                        <Chip
                          label="System"
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {role.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <SecurityIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        {/* TODO: Show real permissions count if available */}
                        N/A
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {role.datecreated
                        ? new Date(role.datecreated).toLocaleDateString()
                        : "-"}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box
                      sx={{
                        display: "flex",
                        gap: 0.5,
                        justifyContent: "center",
                      }}
                    >
                      <Tooltip title="Edit Role">
                        <span>
                          <IconButton
                            size="small"
                            onClick={() =>
                              router.push(
                                `/roles/edit?slug=${role.slug}`
                              )
                            }
                            disabled={loading || !canManage}
                            color="primary"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip title="Manage Permissions">
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => onAssignPermissions(role)}
                            disabled={loading || !canManage}
                            color="info"
                          >
                            <SecurityIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip
                        title={
                          isSystemRole(role)
                            ? "Cannot delete system roles"
                            : "Delete Role"
                        }
                      >
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => onDelete(role)}
                            disabled={
                              loading || isSystemRole(role) || !canManage
                            }
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={sortedRoles.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(_, newPage) => setPage(newPage)}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
      />
    </Paper>
  );
}
