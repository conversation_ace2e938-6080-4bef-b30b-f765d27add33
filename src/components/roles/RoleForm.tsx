"use client";

import { Role, SYSTEM_PERMISSIONS } from "@/types/role";
import {
  Alert,
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useAppSelector } from "@/store/hooks";

interface RoleFormProps {
  role?: Role; // If provided, form is in edit mode
  rolePermissions?: {
    [module: string]: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
    };
  }; // Permissions for the role being edited
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
  currentUserRole?: string;
}

// Helper: get all unique modules/resources
const MODULES = Array.from(new Set(SYSTEM_PERMISSIONS.map((p) => p.resource)));
const ACTIONS = [
  { key: "view", label: "View" },
  { key: "create", label: "Create" },
  { key: "update", label: "Update" },
  { key: "delete", label: "Delete" },
];

export function RoleForm({
  role,
  onSubmit,
  onCancel,
  loading = false,
  error,
  currentUserRole = "admin",
}: RoleFormProps) {
  const theme = useTheme();
  const isEditMode = !!role;

  // Get allRolesPermissions from Redux
  const allRolesPermissions = useAppSelector(
    (state) => state.roles.allRolesPermissions?.roles || []
  );

  // Find permissions for the current role from Redux if not provided as a prop
  const reduxRolePermissions = React.useMemo(() => {
    if (!isEditMode || !role) return undefined;
    const found = allRolesPermissions.find(
      (r) => r.role_slug === role.slug || r.role_name === role.name
    );

    if (!found) return undefined;
    // Convert array of module_permissions to the expected object shape
    const perms: {
      [module: string]: {
        view: boolean;
        create: boolean;
        update: boolean;
        delete: boolean;
      };
    } = {};
    found.module_permissions.forEach((mod) => {
      perms[mod.module_slug] = {
        view: mod.view,
        create: mod.create,
        update: mod.update,
        delete: mod.delete,
      };
    });
    return perms;
  }, [isEditMode, role, allRolesPermissions]);

  // Form state for name/description
  const [name, setName] = useState(role ? role.name : "");
  const [description, setDescription] = useState(role ? role.description : ""); // Permission matrix state: { [module]: { view: boolean, create: boolean, update: boolean, delete: boolean } }
  const [permissionMatrix, setPermissionMatrix] = useState<{
    [module: string]: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
    };
  }>(() => {
    // Priority: prop > redux > empty
    console.log(reduxRolePermissions, 'reduxRolePermissions')
    const source = reduxRolePermissions || {};
    const matrix: any = {};
    MODULES.forEach((module) => {
      matrix[module] = {
        view: source[module]?.view || false,
        create: source[module]?.create || false,
        update: source[module]?.update || false,
        delete: source[module]?.delete || false,
      };
    });
    console.log(matrix, 'matrix')
    return matrix;
  });

  // Update permission matrix when rolePermissions or reduxRolePermissions change
  useEffect(() => {
    const source =  reduxRolePermissions;
    if (source) {
      const matrix: any = {};
      MODULES.forEach((module) => {
        matrix[module] = {
          view: source[module]?.view || false,
          create: source[module]?.create || false,
          update: source[module]?.update || false,
          delete: source[module]?.delete || false,
        };
      });
      setPermissionMatrix(matrix);
    }
  }, [reduxRolePermissions]);

  // Handle checkbox change
  const handlePermissionChange = (
    module: string,
    action: string,
    checked: boolean
  ) => {
    setPermissionMatrix((prev) => ({
      ...prev,
      [module]: {
        ...prev[module],
        [action]: checked,
      },
    }));
  };

  // Handle select all for an action (column)
  const handleSelectAllAction = (action: string, checked: boolean) => {
    setPermissionMatrix((prev) => {
      const updated = { ...prev };
      MODULES.forEach((module) => {
        updated[module] = { ...updated[module], [action]: checked };
      });
      return updated;
    });
  };

  // Handle select all for a module (row)
  const handleSelectAllModule = (module: string, checked: boolean) => {
    setPermissionMatrix((prev) => ({
      ...prev,
      [module]: {
        view: checked,
        create: checked,
        update: checked,
        delete: checked,
      },
    }));
  };  // Form submission
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!name.trim()) return;
    if (!description.trim()) return;

    // Get original permissions for comparison
    const originalPermissions = reduxRolePermissions || {};

    const permissions: {
      [module: string]: {
        view: boolean;
        create: boolean;
        update: boolean;
        delete: boolean;
      };
    } = {};
    
    MODULES.forEach((module) => {
      const current = permissionMatrix[module];
      const original = originalPermissions[module] || {
        view: false,
        create: false,
        update: false,
        delete: false,
      };
      const hasChanged = (
        current.view !== original.view ||
        current.create !== original.create ||
        current.update !== original.update ||
        current.delete !== original.delete
      );
      if (hasChanged) {
        permissions[module] = {
          view: current.view,
          create: current.create,
          update: current.update,
          delete: current.delete,
        };
      }
    });
    const apiData = {
      role: {
        name: name.trim(),
        description: description.trim(),
      },
      permissions,
    };
    await onSubmit(apiData);
  };

  // Render
  return (
    <Box component="form" onSubmit={handleFormSubmit}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      <Typography variant="h6" gutterBottom>
        Basic Information
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Role Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            fullWidth
            required
            disabled={loading}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            label="Description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            fullWidth
            required
            multiline
            rows={3}
            disabled={loading}
          />
        </Grid>
      </Grid>
      <Divider sx={{ my: 3 }} />
      <Typography variant="h6" sx={{ mb: 2 }}>
        Permissions
      </Typography>
      <Box sx={{ overflowX: "auto", mb: 2 }}>
        <table style={{ width: "100%", borderCollapse: "collapse" }}>
          <thead>
            <tr>
              <th style={{ textAlign: "left", padding: 8 }}>Module</th>
              {ACTIONS.map((action) => (
                <th
                  key={action.key}
                  style={{ textAlign: "center", padding: 8 }}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={MODULES.every(
                          (module) =>
                            permissionMatrix[module][
                              action.key as
                                | "view"
                                | "create"
                                | "update"
                                | "delete"
                            ]
                        )}
                        indeterminate={
                          MODULES.some(
                            (module) =>
                              permissionMatrix[module][
                                action.key as
                                  | "view"
                                  | "create"
                                  | "update"
                                  | "delete"
                              ]
                          ) &&
                          !MODULES.every(
                            (module) =>
                              permissionMatrix[module][
                                action.key as
                                  | "view"
                                  | "create"
                                  | "update"
                                  | "delete"
                              ]
                          )
                        }
                        onChange={(e) =>
                          handleSelectAllAction(action.key, e.target.checked)
                        }
                        disabled={loading}
                      />
                    }
                    label={action.label}
                  />
                </th>
              ))}
              <th style={{ textAlign: "center", padding: 8 }}>All</th>
            </tr>
          </thead>
          <tbody>
            {MODULES.map((module) => {
              console.log(module, 'module',permissionMatrix[module]
                        )
              return(
              <tr key={module} style={{ borderBottom: "1px solid #eee" }}>
                <td style={{ padding: 8 }}>{module}</td>
                {ACTIONS.map((action) => {
                  console.log(action, 'action')
                  return(
                  <td
                    key={action.key}
                    style={{ textAlign: "center", padding: 8 }}
                  >
                    <Checkbox
                      checked={
                        permissionMatrix[module][
                          action.key as "view" | "create" | "update" | "delete"
                        ]
                      }
                      onChange={(e) =>
                        handlePermissionChange(
                          module,
                          action.key,
                          e.target.checked
                        )
                      }
                      disabled={loading}
                    />
                  </td>
                )})}
                <td style={{ textAlign: "center", padding: 8 }}>
                  <Checkbox
                    checked={Object.values(permissionMatrix[module]).every(
                      Boolean
                    )}
                    indeterminate={
                      Object.values(permissionMatrix[module]).some(Boolean) &&
                      !Object.values(permissionMatrix[module]).every(Boolean)
                    }
                    onChange={(e) =>
                      handleSelectAllModule(module, e.target.checked)
                    }
                    disabled={loading}
                  />
                </td>
              </tr>
            )})}
          </tbody>
        </table>
      </Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 3 }}>
        <Button onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={loading}
        >
          {isEditMode ? "Update" : "Save"}
        </Button>
      </Box>
    </Box>
  );
}
