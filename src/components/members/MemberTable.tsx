"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Paper,
  Checkbox,
  IconButton,
  Chip,
  Avatar,
  Box,
  Typography,
  Skeleton,
  useTheme,
  Tooltip,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  VerifiedUser as VerifyIcon,
  Email as EmailIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { Member, MemberWithRelations } from "@/types/member";
import MemberTableRow from "./MemberTableRow";

interface MemberTableProps {
  members: MemberWithRelations[];
  loading?: boolean;
  selectedIds: number[];
  onSelectionChange: (selectedIds: number[]) => void;
  onEdit: (member: Member) => void;
  onDelete: (member: Member) => void;
  onView: (member: Member) => void;
  onVerify: (memberId: number) => void;
  onEmailVerify: (memberId: number) => void;
  onStatusChange: (memberId: number, status: string) => void;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  onSortChange: (sortBy: string, sortOrder: "asc" | "desc") => void;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onBulkDelete?: () => void;
  onBulkExport?: () => void;
  onBulkVerify?: () => void;
}

const TABLE_COLUMNS = [
  { id: "name", label: "Name", sortable: true, width: "200px" },
  { id: "email", label: "Email", sortable: true, width: "250px" },
  {
    id: "membershipTier",
    label: "Tier",
    sortable: true,
    width: "120px",
    align: "center" as const,
  },
  {
    id: "communityStatus",
    label: "Status",
    sortable: true,
    width: "120px",
    align: "center" as const,
  },
  {
    id: "verificationStatus",
    label: "Verification",
    sortable: true,
    width: "120px",
    align: "center" as const,
  },
  {
    id: "organizationCount",
    label: "Organizations",
    sortable: true,
    width: "120px",
    align: "center" as const,
  },
  {
    id: "actions",
    label: "Actions",
    sortable: false,
    width: "150px",
    align: "center" as const,
  },
];

export function MemberTable({
  members,
  loading = false,
  selectedIds,
  onSelectionChange,
  onEdit,
  onDelete,
  onView,
  onVerify,
  onEmailVerify,
  onStatusChange,
  sortBy,
  sortOrder,
  onSortChange,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  onBulkDelete,
  onBulkExport,
  onBulkVerify,
}: MemberTableProps) {
  const theme = useTheme();
  const [bulkMenuAnchor, setBulkMenuAnchor] = useState<null | HTMLElement>(
    null
  );

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      onSelectionChange(members.map((member) => member.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectRow = (memberId: number) => {
    const newSelectedIds = selectedIds.includes(memberId)
      ? selectedIds.filter((id) => id !== memberId)
      : [...selectedIds, memberId];
    onSelectionChange(newSelectedIds);
  };

  const handleSort = (columnId: string) => {
    const newSortOrder =
      sortBy === columnId && sortOrder === "asc" ? "desc" : "asc";
    onSortChange(columnId, newSortOrder);
  };

  const handleBulkMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setBulkMenuAnchor(event.currentTarget);
  };

  const handleBulkMenuClose = () => {
    setBulkMenuAnchor(null);
  };

  const handleBulkAction = (action: "delete" | "export" | "verify") => {
    handleBulkMenuClose();
    switch (action) {
      case "delete":
        onBulkDelete?.();
        break;
      case "export":
        onBulkExport?.();
        break;
      case "verify":
        onBulkVerify?.();
        break;
    }
  };

  if (loading) {
    return (
      <Paper sx={{ width: "100%", overflow: "hidden" }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Skeleton variant="rectangular" width={20} height={20} />
                </TableCell>
                {TABLE_COLUMNS.map((column) => (
                  <TableCell key={column.id}>
                    <Skeleton variant="text" width="80%" />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {[...Array(5)].map((_, index) => (
                <TableRow key={index}>
                  <TableCell padding="checkbox">
                    <Skeleton variant="rectangular" width={20} height={20} />
                  </TableCell>
                  {TABLE_COLUMNS.map((column) => (
                    <TableCell key={column.id}>
                      <Skeleton variant="text" width="90%" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      {/* Bulk Actions Bar */}
      {selectedIds.length > 0 && (
        <Box
          sx={{
            p: { xs: 1.5, md: 2 },
            bgcolor: "primary.main",
            color: "white",
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            alignItems: { xs: "flex-start", sm: "center" },
            justifyContent: "space-between",
            gap: { xs: 1, sm: 0 },
          }}
        >
          <Typography
            variant="body2"
            sx={{ fontSize: { xs: "0.875rem", md: "1rem" } }}
          >
            {selectedIds.length} member{selectedIds.length !== 1 ? "s" : ""}{" "}
            selected
          </Typography>
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              size="small"
              variant="outlined"
              color="inherit"
              onClick={handleBulkMenuOpen}
              startIcon={<MoreVertIcon />}
              sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
            >
              <Box sx={{ display: { xs: "none", sm: "block" } }}>
                Bulk Actions
              </Box>
              <Box sx={{ display: { xs: "block", sm: "none" } }}>Actions</Box>
            </Button>
            <Menu
              anchorEl={bulkMenuAnchor}
              open={Boolean(bulkMenuAnchor)}
              onClose={handleBulkMenuClose}
            >
              <MenuItem onClick={() => handleBulkAction("verify")}>
                <ListItemIcon>
                  <VerifyIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>Verify Selected</ListItemText>
              </MenuItem>
              <MenuItem onClick={() => handleBulkAction("export")}>
                <ListItemIcon>
                  <DownloadIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>Export Selected</ListItemText>
              </MenuItem>
              <MenuItem
                onClick={() => handleBulkAction("delete")}
                sx={{ color: "error.main" }}
              >
                <ListItemIcon>
                  <DeleteIcon fontSize="small" color="error" />
                </ListItemIcon>
                <ListItemText>Delete Selected</ListItemText>
              </MenuItem>
            </Menu>
          </Box>
        </Box>
      )}

      <TableContainer className="responsive-table">
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={
                    selectedIds.length > 0 &&
                    selectedIds.length < members.length
                  }
                  checked={
                    members.length > 0 && selectedIds.length === members.length
                  }
                  onChange={handleSelectAll}
                />
              </TableCell>
              {TABLE_COLUMNS.map((column) => (
                <TableCell
                  key={column.id}
                  sortDirection={sortBy === column.id ? sortOrder : false}
                  align={column.align || "left"}
                  sx={{
                    width: column.width,
                    fontWeight: 600,
                    fontSize: { xs: "0.75rem", md: "0.875rem" },
                    padding: { xs: "8px 4px", md: "16px" },
                  }}
                >
                  {column.sortable ? (
                    <TableSortLabel
                      active={sortBy === column.id}
                      direction={sortBy === column.id ? sortOrder : "asc"}
                      onClick={() => handleSort(column.id)}
                      sx={{ fontSize: { xs: "0.75rem", md: "0.875rem" } }}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {members.map((member) => (
              <MemberTableRow
                key={member.id}
                member={member}
                isSelected={selectedIds.includes(member.id)}
                onSelect={handleSelectRow}
                onEdit={onEdit}
                onDelete={onDelete}
                onView={onView}
                onVerify={onVerify}
                onEmailVerify={onEmailVerify}
                onStatusChange={onStatusChange}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={total}
        rowsPerPage={pageSize}
        page={page - 1}
        onPageChange={(_, newPage) => onPageChange(newPage + 1)}
        onRowsPerPageChange={(event) =>
          onPageSizeChange(parseInt(event.target.value, 10))
        }
        sx={{
          "& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows":
            {
              fontSize: { xs: "0.75rem", md: "0.875rem" },
            },
        }}
      />
    </Paper>
  );
}
