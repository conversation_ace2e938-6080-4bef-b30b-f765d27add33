"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Typography,
  Alert,
  Button,
  Card,
  CardContent,
  IconButton,
  SelectChangeEvent,
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Work as WorkIcon,
  Security as SecurityIcon,
  Flag as FlagIcon,
} from "@mui/icons-material";
import { membersService } from "@/services/members";
import OrganizationSelector from "./OrganizationSelector";
import NewOrganizationForm from "./NewOrganizationForm";

interface MemberFormProps {
  values: any;
  errors: any;
  touched: any;
  handleChange: (e: React.ChangeEvent<any>) => void;
  handleBlur: (e: React.FocusEvent<any>) => void;
  handleSubmit: (e?: React.FormEvent<HTMLFormElement>) => void;
  onCancel: () => void;
  loading?: boolean;
}

const MemberForm: React.FC<MemberFormProps> = ({
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  handleSubmit,
  onCancel,
  loading = false,
}) => {
  const [organizations, setOrganizations] = useState<
    Array<{ id: number; name: string; city?: string; state?: string }>
  >([]);
  const [showNewOrgForm, setShowNewOrgForm] = useState(false);

  useEffect(() => {
    fetchOrganizations();
  }, []);
  const fetchOrganizations = async () => {
    try {
      const orgs = await membersService.getOrganizations();
      setOrganizations(orgs);
    } catch (err) {
      console.error("Failed to fetch organizations:", err);
    }
  };

  const handleSelectChange = (event: SelectChangeEvent<any>) => {
    const syntheticEvent = {
      target: {
        name: event.target.name,
        value: event.target.value,
      },
    } as React.ChangeEvent<any>;
    handleChange(syntheticEvent);
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{ maxWidth: 800, mx: "auto" }}
    >
      {/* Error Display */}
      {Object.keys(errors).length > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Please fix the following errors:
          </Typography>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            {Object.entries(errors).map(([field, error]) => (
              <li key={field}>
                <Typography variant="body2">
                  {typeof error === "string" ? error : (error as any)?.message || "Invalid input"}
                </Typography>
              </li>
            ))}
          </ul>
        </Alert>
      )}

      {/* Personal Information Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <PersonIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Personal Information</Typography>
          </Box>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="firstName"
                label="First Name"
                fullWidth
                value={values.firstName}
                onChange={handleChange}
                onBlur={handleBlur}
                error={!!errors.firstName && touched.firstName}
                helperText={touched.firstName && errors.firstName}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="lastName"
                label="Last Name"
                fullWidth
                value={values.lastName}
                onChange={handleChange}
                onBlur={handleBlur}
                error={!!errors.lastName && touched.lastName}
                helperText={touched.lastName && errors.lastName}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="loginEmail"
                label="Login Email"
                type="email"
                fullWidth
                value={values.loginEmail}
                onChange={handleChange}
                onBlur={handleBlur}
                error={!!errors.loginEmail && touched.loginEmail}
                helperText={touched.loginEmail && errors.loginEmail}
                required
                InputProps={{
                  startAdornment: (
                    <EmailIcon sx={{ mr: 1, color: "text.secondary" }} />
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="personalBusinessEmail"
                label="Personal/Business Email (Optional)"
                type="email"
                fullWidth
                value={values.personalBusinessEmail}
                onChange={handleChange}
                onBlur={handleBlur}
                error={
                  !!errors.personalBusinessEmail &&
                  touched.personalBusinessEmail
                }
                helperText={
                  touched.personalBusinessEmail && errors.personalBusinessEmail
                }
                InputProps={{
                  startAdornment: (
                    <EmailIcon sx={{ mr: 1, color: "text.secondary" }} />
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="phone"
                label="Phone Number"
                fullWidth
                value={values.phone}
                onChange={handleChange}
                onBlur={handleBlur}
                error={!!errors.phone && touched.phone}
                helperText={touched.phone && errors.phone}
                InputProps={{
                  startAdornment: (
                    <PhoneIcon sx={{ mr: 1, color: "text.secondary" }} />
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="professionalTitle"
                label="Professional Title"
                fullWidth
                value={values.professionalTitle}
                onChange={handleChange}
                onBlur={handleBlur}
                error={!!errors.professionalTitle && touched.professionalTitle}
                helperText={
                  touched.professionalTitle && errors.professionalTitle
                }
                InputProps={{
                  startAdornment: (
                    <WorkIcon sx={{ mr: 1, color: "text.secondary" }} />
                  ),
                }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Membership Information Section */}
      {/* Membership Information Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <SecurityIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Membership Information</Typography>
          </Box>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <FormControl 
                fullWidth
                error={!!errors.identityType && touched.identityType}
              >
                <InputLabel>Identity Type</InputLabel>
                <Select
                  name="identityType"
                  label="Identity Type"
                  value={values.identityType}
                  onChange={handleSelectChange}
                  onBlur={handleBlur}
                >
                  <MenuItem value="individual">Individual</MenuItem>
                  <MenuItem value="business">Business</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl 
                fullWidth
                error={!!errors.membershipTier && touched.membershipTier}
              >
                <InputLabel>Membership Tier</InputLabel>
                <Select
                  name="membershipTier"
                  label="Membership Tier"
                  value={values.membershipTier}
                  onChange={handleSelectChange}
                  onBlur={handleBlur}
                >
                  <MenuItem value="basic">Basic</MenuItem>
                  <MenuItem value="premium">Premium</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl 
                fullWidth
                error={!!errors.communityStatus && touched.communityStatus}
              >
                <InputLabel>Community Status</InputLabel>
                <Select
                  name="communityStatus"
                  label="Community Status"
                  value={values.communityStatus}
                  onChange={handleSelectChange}
                  onBlur={handleBlur}
                >
                  <MenuItem value="unverified">Unverified</MenuItem>
                  <MenuItem value="verified">Verified</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    name="loginEmailVerified"
                    checked={values.loginEmailVerified}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                }
                label="Email Verified"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {/* Organizations Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mb: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <BusinessIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Organizations</Typography>
            </Box>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setShowNewOrgForm(true)}
              size="small"
            >
              Create New Organization
            </Button>
          </Box>

          <OrganizationSelector
            organizations={organizations}
            selectedOrganizations={values.organizations || []}
            onAddOrganization={() => {}} // No longer needed, handled by parent
            onRemoveOrganization={() => {}} // No longer needed, handled by parent
          />
        </CardContent>
      </Card>

      {/* Feature Flags Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mb: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <FlagIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Feature Flags</Typography>
            </Box>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => {}} // No longer needed, handled by parent
              size="small"
            >
              Add Feature Flag
            </Button>
          </Box>

          {(values.featureFlags || []).map((flag: any, index: number) => (
            <Box
              key={index}
              sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}
            >
              <TextField
                name={`featureFlags.${index}.featureHandle`}
                label="Feature Handle"
                value={flag.featureHandle}
                onChange={handleChange}
                onBlur={handleBlur}
                size="small"
                sx={{ flexGrow: 1 }}
              />
              <FormControlLabel
                control={
                  <Checkbox
                    name={`featureFlags.${index}.enabled`}
                    checked={flag.enabled}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                }
                label="Enabled"
              />
              <IconButton
                color="error"
                onClick={() => {}} // No longer needed, handled by parent
                size="small"
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          ))}

          {(values.featureFlags || []).length === 0 && (
            <Typography variant="body2" color="text.secondary">
              No feature flags configured
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Auth0 Integration Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Auth0 Integration
          </Typography>

          <FormControlLabel
            control={
              <Checkbox
                name="createAuth0User"
                checked={values.createAuth0User}
                onChange={handleChange}
                onBlur={handleBlur}
              />
            }
            label="Create Auth0 User"
          />

          {values.createAuth0User && (
            <TextField
              name="auth0Password"
              label="Auth0 Password"
              type="password"
              fullWidth
              value={values.auth0Password}
              onChange={handleChange}
              onBlur={handleBlur}
              error={!!errors.auth0Password && touched.auth0Password}
              helperText={touched.auth0Password && errors.auth0Password}
              required
              sx={{ mt: 2 }}
            />
          )}
        </CardContent>
      </Card>

      {/* Notes Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Notes
          </Typography>

          <TextField
            name="notes"
            label="Additional Notes"
            multiline
            rows={4}
            fullWidth
            value={values.notes}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="Add any additional notes about this member..."
          />
        </CardContent>
      </Card>

      {/* Form Actions */}
      <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
        <Button variant="outlined" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" variant="contained" disabled={loading}>
          {loading ? "Saving..." : "Save"}
        </Button>
      </Box>

      {/* New Organization Dialog */}
      <NewOrganizationForm
        open={showNewOrgForm}
        onClose={() => setShowNewOrgForm(false)}
        onSuccess={(org) => {
          // This callback is no longer needed, handled by parent
        }}
      />
    </Box>
  );
};

export default MemberForm;
