'use client';

import React, { useState } from 'react';
import {
  Box,
  Autocomplete,
  TextField,
  Chip,
  Typography,
  IconButton,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  VerifiedUser as VerifiedIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { type OrganizationData } from '@/lib/validations/member';

interface OrganizationSelectorProps {
  organizations: Array<{ id: number; name: string; city?: string; state?: string }>;
  selectedOrganizations: OrganizationData[];
  onAddOrganization: (org: OrganizationData) => void;
  onRemoveOrganization: (index: number) => void;
}

const OrganizationSelector: React.FC<OrganizationSelectorProps> = ({
  organizations,
  selectedOrganizations,
  onAddOrganization,
  onRemoveOrganization,
}) => {
  const [searchValue, setSearchValue] = useState('');

  const handleAddOrganization = (org: { id: number; name: string; city?: string; state?: string } | null) => {
    if (!org) return;

    // Check if organization is already selected
    const isAlreadySelected = selectedOrganizations.some(selected => selected.id === org.id);
    if (isAlreadySelected) {
      return;
    }

    // Convert to OrganizationData format
    const newOrg: OrganizationData = {
      id: org.id,
      name: org.name,
      city: org.city,
      state: org.state,
    };

    onAddOrganization(newOrg);
    setSearchValue('');
  };

  const getOrganizationDisplayName = (org: { id: number; name: string; city?: string; state?: string }) => {
    if (org.city && org.state) {
      return `${org.name}, ${org.city}, ${org.state}`;
    } else if (org.city) {
      return `${org.name}, ${org.city}`;
    } else if (org.state) {
      return `${org.name}, ${org.state}`;
    }
    return org.name;
  };

  const getOrganizationStatus = (org: OrganizationData) => {
    // Mock verification status - in real app this would come from the organization data
    const isVerified = Math.random() > 0.3; // 70% chance of being verified
    const hasIssues = Math.random() > 0.8; // 20% chance of having issues
    
    if (hasIssues) return 'warning';
    if (isVerified) return 'verified';
    return 'unverified';
  };

  return (
    <Box>
      {/* Search and Add Organization */}
      <Box sx={{ mb: 2 }}>
        <Autocomplete
          options={organizations}
          getOptionLabel={(option) => getOrganizationDisplayName(option)}
          value={null}
          inputValue={searchValue}
          onInputChange={(_, newValue) => setSearchValue(newValue)}
          onChange={(_, value) => handleAddOrganization(value)}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Search and add organizations"
              placeholder="Type to search organizations..."
              size="small"
              InputProps={{
                ...params.InputProps,
                startAdornment: <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          )}
          renderOption={(props, option) => (
            <Box component="li" {...props}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body2">{option.name}</Typography>
                  {(option.city || option.state) && (
                    <Typography variant="caption" color="text.secondary">
                      {option.city && option.state ? `${option.city}, ${option.state}` : option.city || option.state}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>
          )}
        />
      </Box>

      {/* Selected Organizations */}
      {selectedOrganizations.length > 0 ? (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Selected Organizations ({selectedOrganizations.length})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedOrganizations.map((org, index) => {
              const status = getOrganizationStatus(org);
              return (
                <Chip
                  key={`${org.id}-${index}`}
                  label={getOrganizationDisplayName(org as any)}
                  onDelete={() => onRemoveOrganization(index)}
                  deleteIcon={<DeleteIcon />}
                  icon={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {status === 'verified' && (
                        <Tooltip title="Verified Organization">
                          <VerifiedIcon sx={{ fontSize: 16, color: 'success.main' }} />
                        </Tooltip>
                      )}
                      {status === 'warning' && (
                        <Tooltip title="Organization has issues">
                          <WarningIcon sx={{ fontSize: 16, color: 'warning.main' }} />
                        </Tooltip>
                      )}
                      {status === 'unverified' && (
                        <BusinessIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      )}
                    </Box>
                  }
                  variant={status === 'warning' ? 'outlined' : 'filled'}
                  color={status === 'warning' ? 'warning' : status === 'verified' ? 'success' : 'default'}
                  sx={{
                    '& .MuiChip-deleteIcon': {
                      color: 'error.main',
                    },
                  }}
                />
              );
            })}
          </Box>
        </Box>
      ) : (
        <Alert severity="info" sx={{ mb: 2 }}>
          No organizations selected. Use the search above to add organizations.
        </Alert>
      )}

      {/* Organization Details (if any selected) */}
      {selectedOrganizations.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Organization Details
          </Typography>
          {selectedOrganizations.map((org, index) => (
            <Box
              key={`${org.id}-${index}`}
              sx={{
                p: 2,
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                mb: 1,
                backgroundColor: 'background.paper',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {org.name}
                  </Typography>
                  {(org.city || org.state) && (
                    <Typography variant="body2" color="text.secondary">
                      {org.city && org.state ? `${org.city}, ${org.state}` : org.city || org.state}
                    </Typography>
                  )}
                  {org.phone && (
                    <Typography variant="body2" color="text.secondary">
                      Phone: {org.phone}
                    </Typography>
                  )}
                  {org.email && (
                    <Typography variant="body2" color="text.secondary">
                      Email: {org.email}
                    </Typography>
                  )}
                </Box>
                <IconButton
                  size="small"
                  color="error"
                  onClick={() => onRemoveOrganization(index)}
                  sx={{ ml: 1 }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default OrganizationSelector; 