'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  useTheme,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as StableIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { memberAnalyticsService } from '@/services/memberAnalytics';
import {
  MemberGrowthAnalytics,
  MembershipTierAnalytics,
  CommunityStatusAnalytics,
  VerificationStatusAnalytics,
  AnalyticsExportOptions,
} from '@/types/analytics';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface MemberAnalyticsProps {
  dateRange?: {
    start: string;
    end: string;
  };
}

const MemberAnalytics: React.FC<MemberAnalyticsProps> = ({ dateRange }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [exporting, setExporting] = useState(false);
  
  const [memberGrowth, setMemberGrowth] = useState<MemberGrowthAnalytics | null>(null);
  const [membershipTiers, setMembershipTiers] = useState<MembershipTierAnalytics | null>(null);
  const [communityStatus, setCommunityStatus] = useState<CommunityStatusAnalytics | null>(null);
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatusAnalytics | null>(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [growth, tiers, community, verification] = await Promise.all([
        memberAnalyticsService.getMemberGrowthAnalytics(dateRange),
        memberAnalyticsService.getMembershipTierAnalytics(),
        memberAnalyticsService.getCommunityStatusAnalytics(),
        memberAnalyticsService.getVerificationStatusAnalytics(),
      ]);

      setMemberGrowth(growth);
      setMembershipTiers(tiers);
      setCommunityStatus(community);
      setVerificationStatus(verification);
    } catch (err: any) {
      console.error('Analytics data fetch error:', err);
      setError(err.message || 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'json') => {
    try {
      setExporting(true);
      const options: AnalyticsExportOptions = {
        format,
        dataType: 'all',
        dateRange,
      };

      const result = await memberAnalyticsService.exportAnalyticsData(options);
      
      // Create download link
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err: any) {
      console.error('Export error:', err);
      setError('Failed to export data');
    } finally {
      setExporting(false);
    }
  };

  const getGrowthTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUpIcon sx={{ color: 'success.main' }} />;
      case 'decreasing':
        return <TrendingDownIcon sx={{ color: 'error.main' }} />;
      default:
        return <StableIcon sx={{ color: 'warning.main' }} />;
    }
  };

  const getGrowthTrendColor = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return 'success.main';
      case 'decreasing':
        return 'error.main';
      default:
        return 'warning.main';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
          Member Analytics
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchAnalyticsData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={() => handleExport('csv')}
            disabled={exporting}
          >
            Export CSV
          </Button>
        </Box>
      </Box>

      {/* Member Growth Section */}
      {memberGrowth && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Member Growth
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getGrowthTrendIcon(memberGrowth.growthTrend)}
                <Typography
                  variant="body2"
                  sx={{ color: getGrowthTrendColor(memberGrowth.growthTrend) }}
                >
                  {memberGrowth.growthTrend.charAt(0).toUpperCase() + memberGrowth.growthTrend.slice(1)}
                </Typography>
              </Box>
            </Box>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Box sx={{ height: 300 }}>
                  <Line
                    data={{
                      labels: memberGrowth.timeSeries.map(item => item.date),
                      datasets: [
                        {
                          label: 'Total Members',
                          data: memberGrowth.timeSeries.map(item => item.value),
                          borderColor: theme.palette.primary.main,
                          backgroundColor: theme.palette.primary.main + '20',
                          fill: true,
                          tension: 0.4,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { display: false },
                        tooltip: {
                          mode: 'index',
                          intersect: false,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: { color: theme.palette.divider },
                        },
                        x: {
                          grid: { color: theme.palette.divider },
                        },
                      },
                    }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {memberGrowth.averageGrowthRate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Average Growth Rate
                    </Typography>
                  </Box>
                  <Divider />
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Recent Growth
                    </Typography>
                    {memberGrowth.monthlyGrowth.slice(-3).map((month, index) => (
                      <Box key={month.period} sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                        <Typography variant="body2">{month.period}</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          +{month.newMembers} ({month.growthRate.toFixed(1)}%)
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Membership Tiers Section */}
      {membershipTiers && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Membership Tier Distribution
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ height: 300 }}>
                  <Doughnut
                    data={{
                      labels: membershipTiers.distribution.map(tier => tier.tier.charAt(0).toUpperCase() + tier.tier.slice(1)),
                      datasets: [
                        {
                          data: membershipTiers.distribution.map(tier => tier.count),
                          backgroundColor: [
                            theme.palette.primary.main,
                            theme.palette.secondary.main,
                            theme.palette.warning.main,
                            theme.palette.error.main,
                          ],
                          borderWidth: 2,
                          borderColor: theme.palette.background.paper,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { position: 'bottom' },
                        tooltip: {
                          callbacks: {
                            label: (context) => {
                              const tier = membershipTiers.distribution[context.dataIndex];
                              return `${context.label}: ${tier.count} (${tier.percentage.toFixed(1)}%)`;
                            },
                          },
                        },
                      },
                    }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      ${membershipTiers.totalRevenue.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                  </Box>
                  <Divider />
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      Tier Breakdown
                    </Typography>
                    {membershipTiers.distribution.map((tier) => (
                      <Box key={tier.tier} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={tier.tier.charAt(0).toUpperCase() + tier.tier.slice(1)}
                            size="small"
                            variant="outlined"
                          />
                          <Typography variant="body2">
                            {tier.count} members
                          </Typography>
                        </Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {tier.percentage.toFixed(1)}%
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Community Status Section */}
      {communityStatus && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Community Status Breakdown
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Box sx={{ height: 300 }}>
                  <Bar
                    data={{
                      labels: communityStatus.statusBreakdown.map(status => 
                        status.status.charAt(0).toUpperCase() + status.status.slice(1)
                      ),
                      datasets: [
                        {
                          label: 'Members',
                          data: communityStatus.statusBreakdown.map(status => status.count),
                          backgroundColor: [
                            theme.palette.success.main,
                            theme.palette.warning.main,
                            theme.palette.info.main,
                            theme.palette.error.main,
                          ],
                          borderRadius: 4,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { display: false },
                        tooltip: {
                          callbacks: {
                            label: (context) => {
                              const status = communityStatus.statusBreakdown[context.dataIndex];
                              return `${context.label}: ${status.count} (${status.percentage.toFixed(1)}%)`;
                            },
                          },
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: { color: theme.palette.divider },
                        },
                        x: {
                          grid: { display: false },
                        },
                      },
                    }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {communityStatus.verificationMetrics.verificationRate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Verification Rate
                    </Typography>
                  </Box>
                  <Divider />
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      Verification Metrics
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Total Verified:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {communityStatus.verificationMetrics.totalVerified}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Pending:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {communityStatus.verificationMetrics.pendingVerifications}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Avg. Time:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {communityStatus.verificationMetrics.averageVerificationTime.toFixed(1)} days
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Verification Status Section */}
      {verificationStatus && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Verification Status Metrics
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {verificationStatus.processingMetrics.completionRate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completion Rate
                    </Typography>
                  </Box>
                  <Divider />
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      Processing Metrics
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Total Completed:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {verificationStatus.processingMetrics.totalCompleted}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Backlog Size:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {verificationStatus.processingMetrics.backlogSize}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Avg. Processing Time:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {verificationStatus.processingMetrics.averageProcessingTime.toFixed(1)} days
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ height: 300 }}>
                  <Doughnut
                    data={{
                      labels: verificationStatus.statusBreakdown.map(status => 
                        status.status.replace('_', ' ').charAt(0).toUpperCase() + 
                        status.status.replace('_', ' ').slice(1)
                      ),
                      datasets: [
                        {
                          data: verificationStatus.statusBreakdown.map(status => status.count),
                          backgroundColor: [
                            theme.palette.success.main,
                            theme.palette.warning.main,
                            theme.palette.info.main,
                            theme.palette.error.main,
                          ],
                          borderWidth: 2,
                          borderColor: theme.palette.background.paper,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { position: 'bottom' },
                        tooltip: {
                          callbacks: {
                            label: (context) => {
                              const status = verificationStatus.statusBreakdown[context.dataIndex];
                              return `${context.label}: ${status.count} (${status.percentage.toFixed(1)}%)`;
                            },
                          },
                        },
                      },
                    }}
                  />
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default MemberAnalytics; 