'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
} from '@mui/material';
import {
  EmojiEvents as AwardIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { MemberWithRelations, MemberAward } from '@/types/member';

interface MemberAwardsTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
}

const MemberAwardsTab: React.FC<MemberAwardsTabProps> = ({
  member,
  onMemberUpdate,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getAwardStatusColor = (award: MemberAward) => {
    if (award.isDisqualified) return 'error';
    if (award.isWinner) return 'success';
    if (award.isQualified) return 'primary';
    if (award.isJudged) return 'warning';
    return 'default';
  };

  const getAwardStatusIcon = (award: MemberAward) => {
    if (award.isDisqualified) return <ErrorIcon />;
    if (award.isWinner) return <CheckIcon />;
    if (award.isQualified) return <CheckIcon />;
    if (award.isJudged) return <ScheduleIcon />;
    return <WarningIcon />;
  };

  const getAwardStatusText = (award: MemberAward) => {
    if (award.isDisqualified) return 'Disqualified';
    if (award.isWinner) return 'Winner';
    if (award.isQualified) return 'Qualified';
    if (award.isJudged) return 'Under Review';
    return 'Pending';
  };

  return (
    <Box sx={{ px: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Award Applications ({member.awards?.length || 0})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AwardIcon />}
          onClick={() => {
            // TODO: Implement new award application
            console.log('New award application for member:', member.id);
          }}
        >
          New Application
        </Button>
      </Box>

              {!member.awards || member.awards.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <AwardIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Award Applications
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This member hasn't applied for any awards yet.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={2}>
          {member.awards.map((award: MemberAward, index: number) => (
            <Grid item xs={12} md={6} key={index}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6">
                      Award #{award.awardListingElementId}
                    </Typography>
                    <Chip
                      icon={getAwardStatusIcon(award)}
                      label={getAwardStatusText(award)}
                      color={getAwardStatusColor(award) as any}
                      size="small"
                    />
                  </Box>

                  <List dense>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Status"
                        secondary={award.status}
                      />
                    </ListItem>
                    {award.progress !== undefined && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Progress"
                          secondary={`${award.progress}% complete`}
                        />
                      </ListItem>
                    )}
                    {award.startedDate && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Started"
                          secondary={formatDate(award.startedDate)}
                        />
                      </ListItem>
                    )}
                    {award.submittedDate && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Submitted"
                          secondary={formatDate(award.submittedDate)}
                        />
                      </ListItem>
                    )}
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Created"
                        secondary={formatDate(award.dateCreated)}
                      />
                    </ListItem>
                  </List>

                  <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                    {award.isPreviousWinner && (
                      <Chip label="Previous Winner" color="success" size="small" variant="outlined" />
                    )}
                    {award.isPaid && (
                      <Chip label="Paid" color="primary" size="small" variant="outlined" />
                    )}
                    {award.winnerTypes && (
                      <Chip label={award.winnerTypes} color="secondary" size="small" variant="outlined" />
                    )}
                  </Box>

                  {award.applicationLink && (
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ mt: 2 }}
                      onClick={() => window.open(award.applicationLink, '_blank')}
                    >
                      View Application
                    </Button>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default MemberAwardsTab; 