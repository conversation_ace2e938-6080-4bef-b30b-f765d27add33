'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Compare as CompareIcon,
  VerifiedUser as VerifyIcon,
  History as HistoryIcon,
} from '@mui/icons-material';
import { MemberWithRelations, MemberVerifiedData } from '@/types/member';
import { membersService } from '@/services/members';
import MemberStatusChip from './MemberStatusChip';

interface MemberVerificationTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
}

const MemberVerificationTab: React.FC<MemberVerificationTabProps> = ({
  member,
  onMemberUpdate,
}) => {
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const verifiedData = member.verifiedData;

  const handleApproveVerification = async () => {
    try {
      setLoading(true);
      setError(null);
      await membersService.verifyMember({
        memberId: member.id,
        verificationType: 'manual',
        data: {
          firstName: member.firstname,
          lastName: member.lastname,
          email: member.loginemail,
          phone: member.phone,
          professionalTitle: member.professionaltitle,
        },
      });
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || 'Failed to approve verification');
    } finally {
      setLoading(false);
    }
  };

  const handleRejectVerification = async () => {
    try {
      setLoading(true);
      setError(null);
      // TODO: Implement reject verification API call
      console.log('Rejecting verification for member:', member.id, 'Reason:', rejectReason);
      setRejectDialogOpen(false);
      setRejectReason('');
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || 'Failed to reject verification');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const ComparisonField = ({ 
    label, 
    originalValue, 
    verifiedValue, 
    fieldName 
  }: { 
    label: string; 
    originalValue?: string; 
    verifiedValue?: string; 
    fieldName: string;
  }) => {
    const hasChanges = originalValue !== verifiedValue;
    
    return (
      <ListItem sx={{ px: 0 }}>
        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {label}
              {hasChanges && (
                <Chip label="Changed" color="warning" size="small" />
              )}
            </Box>
          }
          secondary={
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" component="span">
                  <strong>Original:</strong>
                </Typography>
                <Typography variant="body2" component="span">
                  {originalValue || 'Not provided'}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary" component="span">
                  <strong>Verified:</strong>
                </Typography>
                <Typography variant="body2" color={hasChanges ? 'primary.main' : 'inherit'} component="span">
                  {verifiedValue || 'Not provided'}
                </Typography>
              </Grid>
            </Grid>
          }
        />
      </ListItem>
    );
  };

  return (
    <Box sx={{ px: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Verification Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Verification Status
            </Typography>
            {verifiedData && (
              <MemberStatusChip
                type="verificationStatus"
                value={verifiedData.verificationStatus}
                size="medium"
              />
            )}
          </Box>
          
          {verifiedData ? (
            <List dense>
              <ListItem sx={{ px: 0 }}>
                <ListItemText
                  primary="Verification Type"
                  secondary={verifiedData.verificationType}
                />
              </ListItem>
              <ListItem sx={{ px: 0 }}>
                <ListItemText
                  primary="Last Updated"
                  secondary={formatDate(verifiedData.dateUpdated)}
                />
              </ListItem>
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No verification data available for this member.
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Data Comparison */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Data Comparison
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Compare original member data with verified information
          </Typography>
          
          <List>
            <ComparisonField
              label="First Name"
              originalValue={member.firstname}
              verifiedValue={verifiedData?.firstName}
              fieldName="firstName"
            />
            <Divider />
            <ComparisonField
              label="Last Name"
              originalValue={member.lastname}
              verifiedValue={verifiedData?.lastName}
              fieldName="lastName"
            />
            <Divider />
            <ComparisonField
              label="Email"
              originalValue={member.loginemail}
              verifiedValue={verifiedData?.email}
              fieldName="email"
            />
            <Divider />
            <ComparisonField
              label="Phone"
              originalValue={member.phone}
              verifiedValue={verifiedData?.phone}
              fieldName="phone"
            />
            <Divider />
            <ComparisonField
              label="Professional Title"
              originalValue={member.professionaltitle}
              verifiedValue={verifiedData?.professionalTitle}
              fieldName="professionaltitle"
            />
          </List>
        </CardContent>
      </Card>

      {/* Verification Actions */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Verification Actions
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              color="success"
              startIcon={<ApproveIcon />}
              onClick={handleApproveVerification}
              disabled={loading || verifiedData?.verificationStatus === 'completed'}
            >
              Approve Verification
            </Button>
            <Button
              variant="outlined"
              color="error"
              startIcon={<RejectIcon />}
              onClick={() => setRejectDialogOpen(true)}
              disabled={loading}
            >
              Reject Verification
            </Button>
            <Button
              variant="outlined"
              startIcon={<HistoryIcon />}
              onClick={() => {
                // TODO: Show verification history
                console.log('Show verification history for member:', member.id);
              }}
            >
              View History
            </Button>
          </Box>

          {verifiedData?.verificationStatus === 'completed' && (
            <Alert severity="success" sx={{ mt: 2 }}>
              This member has been successfully verified.
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onClose={() => setRejectDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Reject Verification</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Reason for Rejection"
              multiline
              rows={4}
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="Please provide a reason for rejecting this verification..."
              required
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleRejectVerification}
            variant="contained"
            color="error"
            disabled={!rejectReason.trim() || loading}
          >
            Reject Verification
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MemberVerificationTab; 