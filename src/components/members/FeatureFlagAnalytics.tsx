'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  Button,
  Chip,
  Divider,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Flag as FlagIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { memberAnalyticsService } from '@/services/memberAnalytics';
import type { FeatureFlagAnalytics as FeatureFlagAnalyticsType, ABTestingAnalytics, FeatureFlagData } from '@/types/analytics';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface FeatureFlagAnalyticsProps {
  dateRange?: {
    start: string;
    end: string;
  };
}

const FeatureFlagAnalytics: React.FC<FeatureFlagAnalyticsProps> = ({ dateRange }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [exporting, setExporting] = useState(false);
  const [updating, setUpdating] = useState<string | null>(null);
  
  const [featureFlagAnalytics, setFeatureFlagAnalytics] = useState<FeatureFlagAnalyticsType | null>(null);
  const [abTestingAnalytics, setABTestingAnalytics] = useState<ABTestingAnalytics | null>(null);
  const [featureFlagData, setFeatureFlagData] = useState<FeatureFlagData[]>([]);

  useEffect(() => {
    fetchFeatureFlagData();
  }, [dateRange]);

  const fetchFeatureFlagData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [analytics, abTesting, features] = await Promise.all([
        memberAnalyticsService.getFeatureFlagAnalytics(),
        memberAnalyticsService.getABTestingAnalytics(),
        memberAnalyticsService.getFeatureFlagData(),
      ]);

      setFeatureFlagAnalytics(analytics);
      setABTestingAnalytics(abTesting);
      setFeatureFlagData(features);
    } catch (err: any) {
      console.error('Feature flag data fetch error:', err);
      setError(err.message || 'Failed to load feature flag data');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'json') => {
    try {
      setExporting(true);
      const result = await memberAnalyticsService.exportAnalyticsData({
        format,
        dataType: 'feature_flags',
        dateRange,
      });
      
      // Create download link
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err: any) {
      console.error('Export error:', err);
      setError('Failed to export data');
    } finally {
      setExporting(false);
    }
  };

  const handleToggleFeature = async (featureHandle: string, enabled: boolean) => {
    try {
      setUpdating(featureHandle);
      await memberAnalyticsService.toggleFeatureFlag(featureHandle, enabled);
      await fetchFeatureFlagData(); // Refresh data
    } catch (err: any) {
      console.error('Error toggling feature flag:', err);
      setError('Failed to toggle feature flag');
    } finally {
      setUpdating(null);
    }
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'success.main';
      case 'good':
        return 'primary.main';
      case 'average':
        return 'warning.main';
      case 'poor':
        return 'error.main';
      default:
        return 'info.main';
    }
  };

  const getPerformanceChipColor = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'success';
      case 'good':
        return 'primary';
      case 'average':
        return 'warning';
      case 'poor':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
          Feature Flag Analytics
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchFeatureFlagData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={() => handleExport('csv')}
            disabled={exporting}
          >
            Export CSV
          </Button>
        </Box>
      </Box>

      {/* Overall Adoption Rate */}
      {featureFlagAnalytics && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                {featureFlagAnalytics.overallAdoptionRate.toFixed(1)}%
              </Typography>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                Overall Feature Adoption Rate
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                <Chip
                  icon={<FlagIcon />}
                  label={`${featureFlagAnalytics.features.length} Features`}
                  variant="outlined"
                />
                <Chip
                  icon={<TrendingUpIcon />}
                  label="Growing"
                  color="success"
                  variant="outlined"
                />
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Adoption by Tier */}
      {featureFlagAnalytics && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Adoption by Member Tier
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ height: 300 }}>
                  <Bar
                    data={{
                      labels: featureFlagAnalytics.adoptionByTier.map(item => item.label),
                      datasets: [
                        {
                          label: 'Adoption Rate (%)',
                          data: featureFlagAnalytics.adoptionByTier.map(item => item.value),
                          backgroundColor: [
                            theme.palette.primary.main,
                            theme.palette.secondary.main,
                            theme.palette.warning.main,
                            theme.palette.error.main,
                          ],
                          borderRadius: 4,
                        },
                      ],
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: { display: false },
                        tooltip: {
                          callbacks: {
                            label: (context) => {
                              const item = featureFlagAnalytics.adoptionByTier[context.dataIndex];
                              return `${context.label}: ${item.value.toFixed(1)}%`;
                            },
                          },
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                          grid: { color: theme.palette.divider },
                          ticks: {
                            callback: (value) => `${value}%`,
                          },
                        },
                        x: {
                          grid: { display: false },
                        },
                      },
                    }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {featureFlagAnalytics.adoptionByTier.map((tier) => (
                    <Box key={tier.label} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {tier.label}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {tier.value.toFixed(1)}% adoption
                        </Typography>
                      </Box>
                      <Box sx={{ width: 100, bgcolor: 'grey.200', borderRadius: 1, overflow: 'hidden' }}>
                        <Box
                          sx={{
                            width: `${tier.value}%`,
                            height: 8,
                            bgcolor: 'primary.main',
                          }}
                        />
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Feature Performance */}
      {featureFlagAnalytics && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Feature Performance Metrics
            </Typography>
            
            <Grid container spacing={3}>
              {featureFlagAnalytics.featurePerformance.map((feature) => (
                <Grid item xs={12} sm={6} md={4} key={feature.featureHandle}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {feature.featureName}
                        </Typography>
                        <Chip
                          label={feature.performance.charAt(0).toUpperCase() + feature.performance.slice(1)}
                          color={getPerformanceChipColor(feature.performance)}
                          size="small"
                        />
                      </Box>
                      
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Engagement:</Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {feature.metrics.engagement}%
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Retention:</Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {feature.metrics.retention}%
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Satisfaction:</Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {feature.metrics.satisfaction}%
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* A/B Testing Results */}
      {abTestingAnalytics && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              A/B Testing Results
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                    {abTestingAnalytics.overallResults.totalTests}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Tests
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                    {abTestingAnalytics.overallResults.significantResults}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Significant Results
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                    {abTestingAnalytics.overallResults.averageLift.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Lift
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Active Tests */}
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Active Tests
            </Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Test Name</TableCell>
                    <TableCell>Variant</TableCell>
                    <TableCell>Participants</TableCell>
                    <TableCell>Conversions</TableCell>
                    <TableCell>Rate</TableCell>
                    <TableCell>Confidence</TableCell>
                    <TableCell>Significant</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {abTestingAnalytics.activeTests.map((test) => (
                    <TableRow key={`${test.testId}-${test.variant}`}>
                      <TableCell>{test.testName}</TableCell>
                      <TableCell>
                        <Chip
                          label={test.variant.charAt(0).toUpperCase() + test.variant.slice(1)}
                          color={test.variant === 'control' ? 'default' : 'primary'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{test.participants.toLocaleString()}</TableCell>
                      <TableCell>{test.conversions.toLocaleString()}</TableCell>
                      <TableCell>{test.conversionRate.toFixed(2)}%</TableCell>
                      <TableCell>{test.confidence.toFixed(1)}%</TableCell>
                      <TableCell>
                        <Chip
                          label={test.isSignificant ? 'Yes' : 'No'}
                          color={test.isSignificant ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Feature Flags Management */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Feature Flags Management
          </Typography>
          
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Feature</TableCell>
                  <TableCell>Adoption Rate</TableCell>
                  <TableCell>Usage by Tier</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {featureFlagData.map((feature) => (
                  <TableRow key={feature.featureHandle}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {feature.featureName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {feature.featureHandle}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {feature.adoptionRate.toFixed(1)}%
                        </Typography>
                        <Box sx={{ width: 60, bgcolor: 'grey.200', borderRadius: 1, overflow: 'hidden' }}>
                          <Box
                            sx={{
                              width: `${feature.adoptionRate}%`,
                              height: 6,
                              bgcolor: 'primary.main',
                            }}
                          />
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        {feature.usageByTier.slice(0, 2).map((tier) => (
                          <Typography key={tier.tier} variant="caption">
                            {tier.tier}: {tier.adoptionRate.toFixed(1)}%
                          </Typography>
                        ))}
                        {feature.usageByTier.length > 2 && (
                          <Typography variant="caption" color="text.secondary">
                            +{feature.usageByTier.length - 2} more
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={feature.enabledCount > 0}
                            onChange={(e) => handleToggleFeature(feature.featureHandle, e.target.checked)}
                            disabled={updating === feature.featureHandle}
                            size="small"
                          />
                        }
                        label=""
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Settings">
                          <IconButton size="small">
                            <SettingsIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default FeatureFlagAnalytics; 