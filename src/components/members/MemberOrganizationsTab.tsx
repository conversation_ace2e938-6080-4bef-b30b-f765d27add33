'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Autocomplete,
  Chip,
  Alert,
  Grid,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  VerifiedUser as VerifyIcon,
} from '@mui/icons-material';
import { MemberWithRelations, Organization } from '@/types/member';
import { membersService } from '@/services/members';
import MemberStatusChip from './MemberStatusChip';

interface MemberOrganizationsTabProps {
  member: MemberWithRelations;
  onMemberUpdate: () => void;
}

const MemberOrganizationsTab: React.FC<MemberOrganizationsTabProps> = ({
  member,
  onMemberUpdate,
}) => {
  const [organizations, setOrganizations] = useState<Array<{ id: number; name: string; city?: string; state?: string }>>([]);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<{ id: number; name: string; city?: string; state?: string } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    try {
      const orgs = await membersService.getOrganizations();
      setOrganizations(orgs);
    } catch (err) {
      console.error('Failed to fetch organizations:', err);
    }
  };

  const handleAddOrganization = async () => {
    if (!selectedOrg) return;

    try {
      setLoading(true);
      setError(null);
      // TODO: Implement API call to add organization relationship
      console.log('Adding organization:', selectedOrg.id, 'to member:', member.id);
      setAddDialogOpen(false);
      setSelectedOrg(null);
      onMemberUpdate();
    } catch (err: any) {
      setError(err.message || 'Failed to add organization');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveOrganization = async (orgId: number) => {
    try {
      setLoading(true);
      // TODO: Implement API call to remove organization relationship
      console.log('Removing organization:', orgId, 'from member:', member.id);
      onMemberUpdate();
    } catch (err: any) {
      setError('Failed to remove organization');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Box sx={{ px: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Organizations ({member.organizations?.length || 0})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
        >
          Add Organization
        </Button>
      </Box>

              {!member.organizations || member.organizations.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <BusinessIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Organizations Associated
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This member is not associated with any organizations yet.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={2}>
          {member.organizations.map((org: Organization) => (
            <Grid item xs={12} md={6} key={org.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {org.name || 'Unnamed Organization'}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                        {org.city && org.state && (
                          <Chip
                            icon={<LocationIcon />}
                            label={`${org.city}, ${org.state}`}
                            size="small"
                            variant="outlined"
                          />
                        )}
                        {org.industry && (
                          <Chip
                            label={org.industry}
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Box>
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveOrganization(org.id)}
                      disabled={loading}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>

                  <List dense>
                    {org.email && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Email"
                          secondary={org.email}
                        />
                      </ListItem>
                    )}
                    {org.phone && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Phone"
                          secondary={org.phone}
                        />
                      </ListItem>
                    )}
                    {org.address1 && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Address"
                          secondary={
                            <Box>
                              {org.address1}
                              {org.address2 && <br />}
                              {org.address2}
                              {org.city && org.state && org.zip && (
                                <>
                                  <br />
                                  {org.city}, {org.state} {org.zip}
                                </>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    )}
                    {org.annualRevenue && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Annual Revenue"
                          secondary={org.annualRevenue}
                        />
                      </ListItem>
                    )}
                    {org.yearFounded && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Founded"
                          secondary={org.yearFounded}
                        />
                      </ListItem>
                    )}
                    {org.companySize && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary="Company Size"
                          secondary={org.companySize}
                        />
                      </ListItem>
                    )}
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary="Created"
                        secondary={formatDate(org.dateCreated)}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Add Organization Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Organization</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Autocomplete
              options={organizations}
              getOptionLabel={(option) => `${option.name}${option.city ? `, ${option.city}` : ''}`}
              value={selectedOrg}
              onChange={(_, value) => setSelectedOrg(value)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Organization"
                  placeholder="Search organizations..."
                  fullWidth
                />
              )}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Can't find the organization? You can create a new one or add it later.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleAddOrganization}
            variant="contained"
            disabled={!selectedOrg || loading}
          >
            Add Organization
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MemberOrganizationsTab; 