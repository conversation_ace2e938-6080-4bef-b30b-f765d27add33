"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Box,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  updateMemberSchema,
  type UpdateMemberData,
  type MemberFormData,
} from "@/lib/validations/member";
import { membersService } from "@/services/members";
import { Member } from "@/types/member";
import MemberForm from "./MemberForm";

interface EditMemberDialogProps {
  open: boolean;
  member: Member;
  onClose: () => void;
  onSuccess: () => void;
}

const EditMemberDialog: React.FC<EditMemberDialogProps> = ({
  open,
  member,
  onClose,
  onSuccess,
}) => {  const [loading, set_loading] = useState(false);
  const [error, set_error] = useState<string | null>(null);
  const [member_data, set_member_data] = useState<MemberFormData | null>(null);
  useEffect(() => {
    if (open && member) {
      load_member_data();
    }
  }, [open, member]);

  const load_member_data = async () => {
    try {
      set_loading(true);
      const full_member_data = await membersService.getMember(
        member.id.toString()
      );
      const form_data: MemberFormData = {
        firstName: full_member_data.firstname || "",
        lastName: full_member_data.lastname || "",
        loginEmail: full_member_data.loginemail || "",
        loginEmailVerified: full_member_data.loginemailverified,
        phone: full_member_data.phone || "",
        personalBusinessEmail: full_member_data.personalbusinessemail || "",
        professionalTitle: full_member_data.professionaltitle || "",
        identityType: full_member_data.identitytype || "individual",
        membershipTier: full_member_data.membershiptier || "basic",
        communityStatus: full_member_data.communitystatus || "unverified",
        hasSeenFirstLoginMessage: full_member_data.hasSeenhirstloginmessage,
        organizations: (full_member_data.organizations || []).map((org) => ({
          ...org,
          name: org.name || "",
        })),
        featureFlags: full_member_data.featureFlags || [],
        createAuth0User: false,
        auth0Password: "",
        notes:
          typeof (full_member_data as any).notes === "string"
            ? (full_member_data as any).notes
            : "",
      };
      set_member_data(form_data);
    } catch (err: any) {
      console.error("Failed to load member data:", err);
      set_error("Failed to load member data. Please try again.");
    } finally {
      set_loading(false);
    }
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required").max(50),
    lastName: Yup.string().required("Last name is required").max(50),
    loginEmail: Yup.string()
      .email("Invalid email")
      .required("Email is required"),
    loginEmailVerified: Yup.boolean(),
    phone: Yup.string(),
    personalBusinessEmail: Yup.string().email("Invalid email").nullable(),
    professionalTitle: Yup.string().max(100),
    identityType: Yup.mixed<"business" | "individual" | "organization">()
      .oneOf(["business", "individual", "organization"])
      .required(),
    membershipTier: Yup.mixed<"basic" | "premium" | "enterprise" | "vip">()
      .oneOf(["basic", "premium", "enterprise", "vip"])
      .required(),
    communityStatus: Yup.string()
      .oneOf(["unverified", "verified", "pending", "rejected"])
      .required(),
    hasSeenFirstLoginMessage: Yup.boolean(),
    organizations: Yup.array()
      .of(
        Yup.object({
          id: Yup.number().optional(),
          name: Yup.string().required(),
          address1: Yup.string().optional(),
          address2: Yup.string().optional(),
          city: Yup.string().optional(),
          state: Yup.string().optional(),
          zip: Yup.string().optional(),
          phone: Yup.string().optional(),
          email: Yup.string().email("Invalid email").optional(),
          annualRevenue: Yup.string().optional(),
          industry: Yup.string().optional(),
          yearFounded: Yup.string().optional(),
          companySize: Yup.string().optional(),
        })
      )
      .default([]),
    featureFlags: Yup.array()
      .of(
        Yup.object({
          id: Yup.number().optional(),
          memberId: Yup.number().optional(),
          featureHandle: Yup.string().required(),
          enabled: Yup.boolean().required(),
        })
      )
      .default([]),
    createAuth0User: Yup.boolean(),
    auth0Password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .when("createAuth0User", {
        is: true,
        then: (schema) =>
          schema.required("Password is required when creating Auth0 user"),
        otherwise: (schema) => schema.optional(),
      }),
    notes: Yup.string().optional(),
  });
  const formik = useFormik({
    initialValues: member_data || {
      firstName: "",
      lastName: "",
      loginEmail: "",
      loginEmailVerified: false,
      phone: "",
      personalBusinessEmail: "",
      professionalTitle: "",
      identityType: "individual",
      membershipTier: "basic",
      communityStatus: "unverified",
      hasSeenFirstLoginMessage: false,
      organizations: [],
      featureFlags: [],
      createAuth0User: false,
      auth0Password: "",
      notes: "",
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      set_loading(true);
      set_error(null);
      try {
        await membersService.updateMember(member.id.toString(), {
          ...values,
          id: member.id,
        } as UpdateMemberData);
        resetForm();
        onSuccess();
      } catch (err: any) {
        set_error(err.message || "Failed to update member. Please try again.");
      } finally {
        set_loading(false);
        setSubmitting(false);
      }
    },
  });

  const handle_close = () => {
    formik.resetForm();
    set_error(null);
    onClose();
  };
  if (loading && !member_data) {
    return (
      <Dialog open={open} onClose={handle_close} maxWidth="md" fullWidth>
        <DialogContent>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: 200,
            }}
          >
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={handle_close} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <EditIcon color="primary" />
          <Typography variant="h6">
            Edit Member: {member.firstname} {member.lastname}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Member Form */}
        {member_data && (
          <MemberForm
            values={formik.values}
            errors={formik.errors}
            touched={formik.touched}
            handleChange={formik.handleChange}
            handleBlur={formik.handleBlur}
            handleSubmit={formik.handleSubmit}
            onCancel={handle_close}
            loading={loading}
          />
        )}
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handle_close}
          disabled={loading}
          startIcon={<CancelIcon />}
        >
          Cancel
        </Button>

        <Button
          variant="contained"
          onClick={() => formik.handleSubmit()}
          disabled={!formik.isValid || loading}
          startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
        >
          {loading ? "Saving..." : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditMemberDialog;
