'use client';

import React from 'react';
import {
  Box,
  Tabs,
  Tab,
  Card,
  CardContent,
} from '@mui/material';
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  EmojiEvents as AwardsIcon,
  VerifiedUser as VerificationIcon,
  Security as SecurityIcon,
  Flag as FeatureFlagsIcon,
} from '@mui/icons-material';
import { MemberWithRelations } from '@/types/member';
import MemberOverviewTab from './MemberOverviewTab';
import MemberOrganizationsTab from './MemberOrganizationsTab';
import MemberAwardsTab from './MemberAwardsTab';
import MemberVerificationTab from './MemberVerificationTab';
import MemberSecurityTab from './MemberSecurityTab';
import MemberFeatureFlagsTab from './MemberFeatureFlagsTab';

interface MemberDetailTabsProps {
  member: MemberWithRelations;
  activeTab: number;
  onTabChange: (tab: number) => void;
  onMemberUpdate: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`member-tabpanel-${index}`}
      aria-labelledby={`member-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `member-tab-${index}`,
    'aria-controls': `member-tabpanel-${index}`,
  };
}

const MemberDetailTabs: React.FC<MemberDetailTabsProps> = ({
  member,
  activeTab,
  onTabChange,
  onMemberUpdate,
}) => {
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    onTabChange(newValue);
  };

  return (
    <Card>
      <CardContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="member detail tabs"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minHeight: 64,
                textTransform: 'none',
                fontSize: '0.875rem',
                fontWeight: 500,
              },
            }}
          >
            <Tab
              label="Overview"
              icon={<PersonIcon />}
              iconPosition="start"
              {...a11yProps(0)}
            />
            <Tab
              label="Organizations"
              icon={<BusinessIcon />}
              iconPosition="start"
              {...a11yProps(1)}
            />
            <Tab
              label="Awards"
              icon={<AwardsIcon />}
              iconPosition="start"
              {...a11yProps(2)}
            />
            <Tab
              label="Verification"
              icon={<VerificationIcon />}
              iconPosition="start"
              {...a11yProps(3)}
            />
            <Tab
              label="Security"
              icon={<SecurityIcon />}
              iconPosition="start"
              {...a11yProps(4)}
            />
            <Tab
              label="Feature Flags"
              icon={<FeatureFlagsIcon />}
              iconPosition="start"
              {...a11yProps(5)}
            />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <MemberOverviewTab
            member={member}
            onMemberUpdate={onMemberUpdate}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <MemberOrganizationsTab
            member={member}
            onMemberUpdate={onMemberUpdate}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <MemberAwardsTab
            member={member}
            onMemberUpdate={onMemberUpdate}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <MemberVerificationTab
            member={member}
            onMemberUpdate={onMemberUpdate}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={4}>
          <MemberSecurityTab
            member={member}
            onMemberUpdate={onMemberUpdate}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={5}>
          <MemberFeatureFlagsTab
            member={member}
            onMemberUpdate={onMemberUpdate}
          />
        </TabPanel>
      </CardContent>
    </Card>
  );
};

export default MemberDetailTabs; 