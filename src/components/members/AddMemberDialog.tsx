"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  Box,
  Typography,
  Alert,
  CircularProgress,
} from "@mui/material";
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  Security as SecurityIcon,
  Flag as FlagIcon,
} from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import { membersService } from "@/services/members";
import MemberForm from "./MemberForm";

interface AddMemberDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const steps = [
  { label: "Basic Information", icon: PersonIcon },
  { label: "Organization Assignment", icon: BusinessIcon },
  { label: "Verification Data", icon: SecurityIcon },
  { label: "Feature Flags", icon: FlagIcon },
];

const AddMemberDialog: React.FC<AddMemberDialogProps> = ({
  open,
  onClose,
  onSuc<PERSON>,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required").max(50),
    lastName: Yup.string().required("Last name is required").max(50),
    loginEmail: Yup.string()
      .email("Invalid email")
      .required("Email is required"),
    loginEmailVerified: Yup.boolean(),
    phone: Yup.string(),
    personalBusinessEmail: Yup.string().email("Invalid email").nullable(),
    professionalTitle: Yup.string().max(100),
    identityType: Yup.mixed<"business" | "individual" | "organization">()
      .oneOf(["business", "individual", "organization"])
      .required(),
    membershipTier: Yup.mixed<"basic" | "premium" | "enterprise" | "vip">()
      .oneOf(["basic", "premium", "enterprise", "vip"])
      .required(),
    communityStatus: Yup.string()
      .oneOf(["unverified", "verified", "pending", "rejected"])
      .required(),
    hasSeenFirstLoginMessage: Yup.boolean(),
    organizations: Yup.array()
      .of(
        Yup.object({
          id: Yup.number().optional(),
          name: Yup.string().required(),
          address1: Yup.string().optional(),
          address2: Yup.string().optional(),
          city: Yup.string().optional(),
          state: Yup.string().optional(),
          zip: Yup.string().optional(),
          phone: Yup.string().optional(),
          email: Yup.string().email("Invalid email").optional(),
          annualRevenue: Yup.string().optional(),
          industry: Yup.string().optional(),
          yearFounded: Yup.string().optional(),
          companySize: Yup.string().optional(),
        })
      )
      .default([]),
    featureFlags: Yup.array()
      .of(
        Yup.object({
          id: Yup.number().optional(),
          memberId: Yup.number().optional(),
          featureHandle: Yup.string().required(),
          enabled: Yup.boolean().required(),
        })
      )
      .default([]),
    createAuth0User: Yup.boolean(),
    auth0Password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .when("createAuth0User", {
        is: true,
        then: (schema) =>
          schema.required("Password is required when creating Auth0 user"),
        otherwise: (schema) => schema.optional(),
      }),
    notes: Yup.string().optional(),
  });

  const initialValues = {
    firstName: "",
    lastName: "",
    loginEmail: "",
    loginEmailVerified: false,
    phone: "",
    personalBusinessEmail: "",
    professionalTitle: "",
    identityType: "individual",
    membershipTier: "basic",
    communityStatus: "unverified",
    hasSeenFirstLoginMessage: false,
    organizations: [],
    featureFlags: [],
    createAuth0User: false,
    auth0Password: "",
    notes: "",
  } as any;

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      setLoading(true);
      setError(null);
      try {
        await membersService.createMember(values);
        resetForm();
        setActiveStep(0);
        onSuccess();
      } catch (err: any) {
        setError(err.message || "Failed to create member. Please try again.");
      } finally {
        setLoading(false);
        setSubmitting(false);
      }
    },
    enableReinitialize: true,
  });

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleClose = () => {
    formik.resetForm();
    setActiveStep(0);
    setError(null);
    onClose();
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 0: // Basic Information
        return (
          formik.values.firstName &&
          formik.values.lastName &&
          formik.values.loginEmail
        );
      case 1: // Organization Assignment
        return true; // Organizations are optional
      case 2: // Verification Data
        return true; // Verification data is optional
      case 3: // Feature Flags
        return true; // Feature flags are optional
      default:
        return false;
    }
  };

  const canProceed = isStepValid(activeStep);

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Enter the member's basic personal and contact information.
            </Typography>

            <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  First Name *
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.firstName || "Not provided"}
                </Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Last Name *
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.lastName || "Not provided"}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Login Email *
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {formik.values.loginEmail || "Not provided"}
              </Typography>
            </Box>

            <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Phone
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.phone || "Not provided"}
                </Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Professional Title
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.professionalTitle || "Not provided"}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Identity Type
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.identityType}
                </Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Membership Tier
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.membershipTier}
                </Typography>
              </Box>
            </Box>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Organization Assignment
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Assign the member to one or more organizations.
            </Typography>

            {formik.values.organizations &&
            formik.values.organizations.length > 0 ? (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Selected Organizations ({formik.values.organizations.length})
                </Typography>
                {formik.values.organizations.map(
                  (
                    org: { name: string; city?: string; state?: string },
                    index: number
                  ) => (
                    <Box
                      key={index}
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: "divider",
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2" fontWeight="medium">
                        {org.name}
                      </Typography>
                      {(org.city || org.state) && (
                        <Typography variant="caption" color="text.secondary">
                          {org.city && org.state
                            ? `${org.city}, ${org.state}`
                            : org.city || org.state}
                        </Typography>
                      )}
                    </Box>
                  )
                )}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No organizations selected. Organizations are optional.
              </Typography>
            )}
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Verification Data
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Configure verification settings and data.
            </Typography>

            <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Community Status
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.communityStatus}
                </Typography>
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Email Verified
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formik.values.loginEmailVerified ? "Yes" : "No"}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Auth0 Integration
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {formik.values.createAuth0User
                  ? "Will create Auth0 user"
                  : "No Auth0 user creation"}
              </Typography>
            </Box>
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Feature Flags
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Configure feature flags for this member.
            </Typography>

            {formik.values.featureFlags &&
            formik.values.featureFlags.length > 0 ? (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Configured Feature Flags ({formik.values.featureFlags.length})
                </Typography>
                {formik.values.featureFlags.map(
                  (
                    flag: { featureHandle: string; enabled: boolean },
                    index: number
                  ) => (
                    <Box
                      key={index}
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: "divider",
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2" fontWeight="medium">
                        {flag.featureHandle}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Status: {flag.enabled ? "Enabled" : "Disabled"}
                      </Typography>
                    </Box>
                  )
                )}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No feature flags configured. Feature flags are optional.
              </Typography>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Add New Member</DialogTitle>

      <DialogContent>
        {/* Stepper */}
        <Box sx={{ mb: 3 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  icon={<step.icon />}
                  optional={index === steps.length - 1 ? undefined : null}
                >
                  {step.label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Step Content */}
        {activeStep === 0 ? (
          <MemberForm
            values={formik.values}
            errors={formik.errors}
            touched={formik.touched}
            handleChange={formik.handleChange}
            handleBlur={formik.handleBlur}
            handleSubmit={formik.handleSubmit}
            onCancel={handleClose}
            loading={loading}
          />
        ) : (
          <Box sx={{ minHeight: 200 }}>{renderStepContent(activeStep)}</Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>

        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}

        {activeStep < steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={!canProceed || loading}
          >
            Next
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={() => formik.handleSubmit()}
            disabled={!formik.isValid || loading}
          >
            {loading ? (
              <>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                Creating Member...
              </>
            ) : (
              "Create Member"
            )}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AddMemberDialog;
