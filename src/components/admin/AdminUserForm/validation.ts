import * as Yup from "yup";
import { getPasswordStrength } from "./utils";

export const RESERVED_USERNAMES = [
  "admin",
  "root",
  "administrator",
  "system",
  "user",
];
export const COMMON_PASSWORDS = [
  "password",
  "12345678",
  "qwerty123",
  "admin123",
  "password123",
];

export const createValidationSchema = (isEditMode: boolean) => {
  const baseSchema = {
    username: Yup.string()
      .min(3, "Username must be at least 3 characters")
      .max(50, "Username must be less than 50 characters")
      .matches(
        /^[a-zA-Z0-9_-]+$/,
        "Username can only contain letters, numbers, underscores, and hyphens"
      )
      .test(
        "no-consecutive-spaces",
        "Username cannot contain consecutive spaces",
        (val) => !val?.includes("  ")
      )
      .test(
        "not-reserved",
        "Username is reserved",
        (val) => !RESERVED_USERNAMES.includes(val?.toLowerCase() || "")
      )
      .required("Username is required"),

    email: Yup.string()
      .email("Please enter a valid email address")
      .max(100, "Email must be less than 100 characters")
      .test("valid-format", "Please enter a valid email format", (val) => {
        if (!val) return true;
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(val);
      })
      .test(
        "no-consecutive-dots",
        "Email cannot contain consecutive dots",
        (val) => !val?.includes("..")
      )
      .test("dot-position", "Email cannot start or end with a dot", (val) =>
        val ? !val.startsWith(".") && !val.endsWith(".") : true
      )
      .required("Email is required"),

    firstname: Yup.string()
      .max(50, "First name must be less than 50 characters")
      .required("First name is required"),

    lastname: Yup.string()
      .max(50, "Last name must be less than 50 characters")
      .required("Last name is required"),

    phone: Yup.string().max(20, "Phone number must be less than 20 characters"),

    countrycode: Yup.string()
      .min(1, "Country code is required")
      .max(5, "Country code must be less than 5 characters")
      .required("Country code is required"),

    roles: Yup.array()
      .of(Yup.string())
      .min(1, "Please select a role")
      .required("Role is required"),
  };

  const passwordSchema = isEditMode
    ? Yup.string().test(
        "password-strength",
        "Password requirements not met",
        function (val) {
          if (!val || val === "") return true;
          const { score } = getPasswordStrength(val);
          return score >= 3;
        }
      )
    : Yup.string()
        .min(8, "Password must be at least 8 characters")
        .max(100, "Password must be less than 100 characters")
        .matches(
          /^(?=.*[a-z])/,
          "Password must contain at least one lowercase letter"
        )
        .matches(
          /^(?=.*[A-Z])/,
          "Password must contain at least one uppercase letter"
        )
        .matches(/^(?=.*\d)/, "Password must contain at least one number")
        .matches(
          /^(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/,
          "Password must contain at least one special character"
        )
        .test("no-spaces", "Password cannot contain spaces", (val) =>
          val ? !/\s/.test(val) : true
        )
        .test(
          "not-common",
          "Password is too common",
          (val) => !COMMON_PASSWORDS.includes(val?.toLowerCase() || "")
        )
        .required("Password is required");

  return Yup.object({
    ...baseSchema,
    ...(isEditMode && { id: Yup.string().required("User ID is required") }),
    temporaryPassword: passwordSchema,
  });
};
