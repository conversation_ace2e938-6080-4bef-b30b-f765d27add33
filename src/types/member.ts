// Main member interface
export interface Member {
  id: number;
  auth0Id: string;
  customerIoId?: string;
  openWaterId?: number;
  firstname?: string;
  lastname?: string;
  loginemail?: string;
  loginemailverified: boolean;
  identitytype?: 'business' | 'individual' | 'organization';
  personalbusinessemail?: string;
  phone?: string;
  professionaltitle?: string;
  membershiptier: 'basic' | 'premium' | 'enterprise' | 'vip';
  communitystatus: 'unverified' | 'verified' | 'pending' | 'rejected';
  hasSeenhirstloginmessage: boolean;
  datecreated: string;
  dateupdated: string;
}

// Organization interface
export interface Organization {
  id: number;
  name?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  email?: string;
  annualRevenue?: string;
  industry?: string;
  yearFounded?: string;
  companySize?: string;
  businessProfileElementId?: number;
  dateCreated: string;
  dateUpdated: string;
}

// Verified data interfaces
export interface MemberVerifiedData {
  memberId: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  professionalTitle?: string;
  verificationStatus: 'requires_review' | 'in_progress' | 'completed' | 'rejected';
  verificationType: 'auto' | 'manual';
  dateCreated: string;
  dateUpdated: string;
}

export interface OrganizationVerifiedData {
  organizationId: number;
  name?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  ein?: string;
  industry?: string;
  foundingYear?: number;
  verificationStatus: 'requires_review' | 'in_progress' | 'completed' | 'rejected';
  verificationType: 'auto' | 'manual';
  dateCreated: string;
  dateUpdated: string;
}

// Awards interface
export interface MemberAward {
  memberId: number;
  organizationId: number;
  awardListingElementId: number;
  status: string;
  progress?: number;
  categories?: any;
  isDisqualified: boolean;
  isPreviousWinner: boolean;
  isQualified: boolean;
  isJudged?: boolean;
  isPaid?: boolean;
  isWinner?: boolean;
  winnerTypes?: string;
  applicationLink?: string;
  startedDate?: string;
  submittedDate?: string;
  dateCreated: string;
  dateUpdated: string;
}

// Blacklist report interface
export interface MemberBlacklistReport {
  memberId: number;
  disposableEmail: boolean;
  appears: boolean;
  frequency: number;
  submitted: string;
  updated: string;
  spamRate: number;
  exists?: boolean;
  inAntispamUpdated?: string;
  dateCreated: string;
  dateUpdated: string;
  isSpam: boolean;
}

// Feature flags interface
export interface MemberFeatureFlag {
  id: number;
  memberId: number;
  featureHandle: string;
  enabled: boolean;
}

// Complete member with relations
export interface MemberWithRelations extends Member {
  organizations: Organization[];
  verifiedData?: MemberVerifiedData;
  awards: MemberAward[];
  blacklistReport?: MemberBlacklistReport;
  featureFlags: MemberFeatureFlag[];
}

// API Response types
export interface MemberStats {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  newThisMonth: number;
  growthRate: number;
}

export interface MemberFilters {
  search: string;
  status: 'all' | 'active' | 'inactive' | 'pending';
  role: 'all' | 'admin' | 'member' | 'premium';
  membershipType: 'all' | 'basic' | 'premium' | 'enterprise';
  industry: string;
  identityType?: 'all' | 'business' | 'individual' | 'organization';
  communityStatus?: 'all' | 'unverified' | 'verified' | 'pending' | 'rejected';
}

export interface MemberSearchParams {
  page: number;
  pageSize: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  filters: MemberFilters;
}

export interface MemberBulkAction {
  memberIds: string[];
  action: 'delete' | 'export' | 'update_status' | 'update_tier' | 'verify';
  data?: any;
}

// Form types for creating/editing members
export interface CreateMemberData {
  firstName: string;
  lastName: string;
  loginEmail: string;
  phone?: string;
  professionalTitle?: string;
  identityType: 'business' | 'individual' | 'organization';
  membershipTier: 'basic' | 'premium' | 'enterprise' | 'vip';
  communityStatus: 'unverified' | 'verified' | 'pending' | 'rejected';
  personalBusinessEmail?: string;
  // Organization data if identityType is 'organization'
  organization?: {
    name: string;
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    zip?: string;
    phone?: string;
    email?: string;
    annualRevenue?: string;
    industry?: string;
    yearFounded?: string;
    companySize?: string;
  };
}

export interface UpdateMemberData extends Partial<CreateMemberData> {
  id: number;
}

// Verification types
export interface VerificationRequest {
  memberId: number;
  verificationType: 'auto' | 'manual';
  data: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    professionalTitle?: string;
  };
}

export interface OrganizationVerificationRequest {
  organizationId: number;
  verificationType: 'auto' | 'manual';
  data: {
    name?: string;
    address?: string;
    city?: string;
    state?: string;
    zip?: string;
    phone?: string;
    ein?: string;
    industry?: string;
    foundingYear?: number;
  };
}

// Award application types
export interface AwardApplication {
  memberId: number;
  organizationId: number;
  awardListingElementId: number;
  categories?: any;
  applicationLink?: string;
}

// Feature flag types
export interface FeatureFlagUpdate {
  memberId: number;
  featureHandle: string;
  enabled: boolean;
}

// Export types
export interface MemberExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  filters?: MemberFilters;
  fields?: string[];
  memberIds?: string[];
}

// Dashboard types
export interface MemberDashboardStats {
  totalMembers: number;
  activeMembers: number;
  newMembersThisMonth: number;
  pendingVerifications: number;
  topIndustries: Array<{
    industry: string;
    count: number;
  }>;
  membershipTierDistribution: Array<{
    tier: string;
    count: number;
    percentage: number;
  }>;
  growthTrend: Array<{
    month: string;
    count: number;
  }>;
}

// Search and filter utilities
export interface MemberSearchResult {
  members: Member[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Comprehensive search filters interface
export interface MemberSearchFilters {
  search?: string;
  membershipTier?: string[];
  communityStatus?: string[];
  identityType?: string[];
  verificationStatus?: string[];
  organizationId?: number;
  hasBlacklistIssues?: boolean;
  dateCreatedFrom?: string;
  dateCreatedTo?: string;
}

// Validation types
export interface MemberValidationErrors {
  firstName?: string;
  lastName?: string;
  loginEmail?: string;
  phone?: string;
  professionalTitle?: string;
  membershipTier?: string;
  communityStatus?: string;
  organization?: {
    name?: string;
    email?: string;
    phone?: string;
  };
}

// Activity and audit types
export interface MemberActivity {
  id: number;
  memberId: number;
  activityType: 'login' | 'profile_update' | 'verification' | 'award_application' | 'membership_change';
  description: string;
  metadata?: any;
  dateCreated: string;
}

export interface MemberAuditLog {
  id: number;
  memberId: number;
  action: string;
  oldValues?: any;
  newValues?: any;
  performedBy: string;
  dateCreated: string;
}

// Column definitions for the table
export interface MemberTableColumn {
  id: keyof Member;
  label: string;
  sortable: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export const MEMBER_TABLE_COLUMNS: MemberTableColumn[] = [
  { id: 'firstname', label: 'Name', sortable: true, width: '200px' },
  { id: 'loginemail', label: 'Email', sortable: true, width: '250px' },
  { id: 'professionaltitle', label: 'Title', sortable: true, width: '200px' },
  { id: 'identitytype', label: 'Type', sortable: true, width: '120px', align: 'center' },
  { id: 'communitystatus', label: 'Status', sortable: true, width: '100px', align: 'center' },
  { id: 'membershiptier', label: 'Tier', sortable: true, width: '120px', align: 'center' },
  { id: 'phone', label: 'Phone', sortable: true, width: '150px' },
  { id: 'datecreated', label: 'Created', sortable: true, width: '120px', align: 'center' },
  { id: 'dateupdated', label: 'Updated', sortable: true, width: '120px', align: 'center' },
]; 