interface ModulePermission {
  create: boolean;
  update: boolean;
  delete: boolean;
  view: boolean;
}

export interface Permission {
  [moduleName: string]: ModulePermission;
}

export interface AdminUser {
  uuid: string;
  username: string;
  email: string;
  firstname: string | null;
  lastname: string | null;
  phone: string | null;
  countrycode: string | null;
  isactive: boolean;
  istemppassword: boolean;
  emailverified: boolean;
  roles: string ;
  createdby: string;
  permissions: Permission[];
}

export interface AdminUserResponse {
  success: boolean;
  message: string;
  user: AdminUser;
  status_code: 200;
}

export interface AdminUserFilters {
  search?: string;
  status?: "all" | "active" | "inactive" | "pending";
  role?: "all" | "super_admin" | "admin" | "moderator";
  department?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface AdminUserSearchParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters: AdminUserFilters;
}

export interface AdminUserListResponse {
  adminUsers: AdminUser[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateAdminUserRequest {
  username: string;
  email: string;
  roles: ("super_admin" | "admin" | "moderator")[];
  phone?: string;
  department?: string;
  notes?: string;
  generatePassword?: boolean;
  sendInvitation?: boolean;
  firstName?: string;
  lastName?: string;
}

export interface UpdateAdminUserRequest {
  password?: string;
  email?: string;
  firstname?: string;
  lastname?: string;
  phone?: string;
  countrycode?: string;
  isactive?: boolean;
  istemppassword?: boolean;
  roles?: string[];
}

export interface BulkActionRequest {
  adminUserIds: string[];
  action: "delete" | "activate" | "deactivate" | "change_role";
  role?: "super_admin" | "admin" | "moderator";
}

export interface AdminUserStats {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  superAdmins: number;
  admins: number;
  moderators: number;
  newThisMonth: number;
}

// Column definitions for the table
export interface AdminUserTableColumn {
  id: keyof AdminUser;
  label: string;
  sortable: boolean;
  width?: string;
  align?: "left" | "center" | "right";
}

export const ADMIN_USER_TABLE_COLUMNS: AdminUserTableColumn[] = [
  { id: "firstname", label: "First Name", sortable: true, width: "150px" },
  { id: "lastname", label: "Last Name", sortable: true, width: "150px" },
  { id: "email", label: "Email", sortable: true, width: "250px" },
  {
    id: "roles",
    label: "Roles",
    sortable: true,
    width: "120px",
    align: "center",
  },
];

// Permission definitions
export const ADMIN_PERMISSIONS = {
  SUPER_ADMIN: [
    "manage_admin_users",
    "manage_members",
    "manage_analytics",
    "manage_settings",
    "view_reports",
    "manage_roles",
    "system_access",
  ],
  ADMIN: [
    "manage_members",
    "manage_analytics",
    "view_reports",
    "limited_settings",
  ],
  MODERATOR: ["view_members", "view_analytics", "basic_reports"],
} as const;

export type AdminRole = keyof typeof ADMIN_PERMISSIONS;

export function getRolePermissions(role: AdminRole): readonly string[] {
  return ADMIN_PERMISSIONS[role] || [];
}

// Updated AdminUser interface to match API response structure
export interface UpdatedAdminUser {
  uuid: string;
  email: string;
  username: string;
  firstname: string;
  lastname: string;
  phone: string;
  countrycode: string;
  isactive: boolean;
  istemppassword: boolean;
  emailverified: boolean;
  roles: string[];
  cognitoid: string;
  createdby: string;
  updatedby: string;
  lastlogin: string;
  datecreated: string;
  dateupdated: string;
}

export interface UpdateAdminUserResponse {
  success?: boolean;
  data?: UpdatedAdminUser;
  error?: string;
  message?: string;
  // Direct API response (when success is not wrapped)
  uuid?: string;
  email?: string;
  username?: string;
  firstname?: string;
  lastname?: string;
  phone?: string;
  countrycode?: string;
  isactive?: boolean;
  istemppassword?: boolean;
  emailverified?: boolean;
  roles?: string[];
  cognitoid?: string;
  createdby?: string;
  updatedby?: string;
  lastlogin?: string;
  datecreated?: string;
  dateupdated?: string;
}
